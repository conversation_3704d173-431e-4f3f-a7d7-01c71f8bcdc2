#!/bin/bash

# Load environment variables from .env.staging file
if [ -f .env.staging ]; then
  export $(grep -v '^#' .env.staging | xargs)
fi

# Now build your Docker image, passing the loaded environment variables as build-args
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/backend_ai_agentq:1.2.0 \
  --platform linux/amd64,linux/arm64 \
  --build-arg DB_HOST="${DB_HOST}" \
  --build-arg DB_PORT="${DB_PORT}" \
  --build-arg DB_USERNAME="${DB_USERNAME}" \
  --build-arg DB_PASSWORD="${DB_PASSWORD}" \
  --build-arg DB_DATABASE="${DB_DATABASE}" \
  --build-arg JWT_SECRET="${JWT_SECRET}" \
  --build-arg GOOGLE_CLOUD_PROJECT_ID="${GOOGLE_CLOUD_PROJECT_ID}" \
  --build-arg GOOGLE_CLOUD_CLIENT_EMAIL="${GOOGLE_CLOUD_CLIENT_EMAIL}" \
  --build-arg GOOGLE_CLOUD_PRIVATE_KEY="${GOOGLE_CLOUD_PRIVATE_KEY}" \
  --build-arg GOOGLE_CLOUD_BUCKET="${GOOGLE_CLOUD_BUCKET}" \
  --build-arg LLM="${LLM}" \
  --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
  --build-arg GEMINI_API_KEY="${GEMINI_API_KEY}" \
  --build-arg REDIS_HOST="${REDIS_HOST}" \
  --build-arg REDIS_PORT="${REDIS_PORT}" \
  --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
  --build-arg REDIS_DB="${REDIS_DB}" \
  --build-arg NODE_ENV="${NODE_ENV}" \
  --push .



# docker buildx build \
#   --platform linux/amd64,linux/arm64 \
#   --build-arg DB_HOST="${DB_HOST}" \
#   --build-arg DB_PORT="${DB_PORT}" \
#   --build-arg DB_USERNAME="${DB_USERNAME}" \
#   --build-arg DB_PASSWORD="${DB_PASSWORD}" \
#   --build-arg DB_DATABASE="${DB_DATABASE}" \
#   --build-arg JWT_SECRET="${JWT_SECRET}" \
#   --build-arg GOOGLE_CLOUD_PROJECT_ID="${GOOGLE_CLOUD_PROJECT_ID}" \
#   --build-arg GOOGLE_CLOUD_CLIENT_EMAIL="${GOOGLE_CLOUD_CLIENT_EMAIL}" \
#   --build-arg GOOGLE_CLOUD_PRIVATE_KEY="${GOOGLE_CLOUD_PRIVATE_KEY}" \
#   --build-arg GOOGLE_CLOUD_BUCKET="${GOOGLE_CLOUD_BUCKET}" \
#   --build-arg LLM="${LLM}" \
#   --build-arg OPENAI_API_KEY="${OPENAI_API_KEY}" \
#   --build-arg GEMINI_API_KEY="${GEMINI_API_KEY}" \
#   --build-arg REDIS_HOST="${REDIS_HOST}" \
#   --build-arg REDIS_PORT="${REDIS_PORT}" \
#   --build-arg REDIS_PASSWORD="${REDIS_PASSWORD}" \
#   --build-arg REDIS_DB="${REDIS_DB}" \
#   --build-arg NODE_ENV="${NODE_ENV}" \
#   -t backend_ai_agentq .