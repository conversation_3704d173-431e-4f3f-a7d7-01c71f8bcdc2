# Database
DB_HOST=
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=azq"D0sVt`<l{PkE
DB_DATABASE=ai_service

# JWT (Must match the secret from the main backend service)
JWT_SECRET=your_jwt_secret_key_change_in_production

# Google Cloud
GOOGLE_CLOUD_PROJECT_ID=orbital-nirvana-447600-j8
GOOGLE_CLOUD_CLIENT_EMAIL=<EMAIL>
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GOOGLE_CLOUD_BUCKET=agentq

# AI
LLM=GEMINI # or OPENAI
OPENAI_API_KEY=***************************************************
GEMINI_API_KEY=AIzaSyC-4i1YvPTCexBUpw7r8PXo6NWRcIBjwoI

# Redis
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

NODE_ENV=staging