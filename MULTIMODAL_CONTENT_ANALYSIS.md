# Multimodal Content Analysis Feature

## Overview

The enhanced AI service now supports multimodal content analysis using Gemini's vision capabilities. This improvement addresses the limitation where embedding models could only process text, missing valuable visual information in uploaded files.

## New Flow Architecture

### Previous Flow
```
User uploads file → Text extraction → Embedding generation → Test case generation
```

### Enhanced Flow
```
User uploads file → Content analysis (Gemini Vision) → Enhanced embedding (text + descriptions) → Test case generation
```

## Supported File Types

### Documents (with potential images)
- PDF files (`application/pdf`)
- Word documents (`.doc`, `.docx`)
- Text files (`.txt`, `.md`, `.html`, `.csv`)
- Excel files (`.xls`, `.xlsx`)

### Images (new)
- JPEG (`.jpg`, `.jpeg`)
- PNG (`.png`)
- GIF (`.gif`)
- WebP (`.webp`)
- BMP (`.bmp`)
- TIFF (`.tiff`, `.tif`)

## Key Features

### 1. Intelligent Content Analysis
- **Multimodal Analysis**: Uses Gemini 2.0 Flash for files containing images
- **Text-Only Analysis**: Optimized analysis for text-based documents
- **Automatic Detection**: Determines the best analysis approach based on file type

### 2. Enhanced Embeddings
- **Context-Rich**: Embeddings include both original text and LLM-generated descriptions
- **Visual Context**: Descriptions of UI elements, diagrams, charts, and workflows
- **Better Test Generation**: More comprehensive test cases based on visual and textual content

### 3. Database Integration
- **Content Descriptions**: Stored in `FileUpload.contentDescription`
- **Analysis Metadata**: Tracking model used, analysis type, processing time
- **Status Tracking**: Monitor analysis progress (`pending`, `processing`, `completed`, `failed`)

## API Endpoints

### Upload Files (Enhanced)
```http
POST /file-upload
Content-Type: multipart/form-data

# Now supports image files in addition to documents
```

### Get Content Analysis
```http
GET /file-upload/{id}/content-analysis
Authorization: Bearer {jwt-token}

Response:
{
  "contentDescription": "This document contains a flowchart showing...",
  "contentAnalysisMetadata": {
    "model": "gemini-2.0-flash",
    "analysisType": "multimodal",
    "hasImages": true,
    "imageCount": 3,
    "confidence": 0.9,
    "processingTime": 2500
  },
  "contentAnalysisStatus": "completed"
}
```

## Configuration

### Environment Variables
```bash
# Enable/disable content analysis
CONTENT_ANALYSIS_ENABLED=true

# Model configuration
CONTENT_ANALYSIS_MODEL=gemini-2.0-flash

# File size limits (50MB default)
CONTENT_ANALYSIS_MAX_FILE_SIZE=52428800

# Processing timeouts (5 minutes default)
CONTENT_ANALYSIS_TIMEOUT=300000

# Retry configuration
CONTENT_ANALYSIS_RETRY_ATTEMPTS=3

# Batch processing
CONTENT_ANALYSIS_BATCH_SIZE=5
```

### Required API Keys
```bash
# Gemini API key for content analysis
GEMINI_API_KEY=your_gemini_api_key_here
```

## Database Migration

Run the migration to add new fields:

```bash
npm run migration:run
```

This adds:
- `contentDescription` (text, nullable)
- `contentAnalysisMetadata` (json, nullable)  
- `contentAnalysisStatus` (varchar, default: 'pending')

## Usage Examples

### 1. Upload PDF with Images
```javascript
const formData = new FormData();
formData.append('file[]', pdfFileWithDiagrams);

const response = await fetch('/file-upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});

// Content analysis will automatically detect images and provide descriptions
```

### 2. Upload UI Screenshots
```javascript
const formData = new FormData();
formData.append('file[]', screenshotFile);

const response = await fetch('/file-upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});

// Gemini will analyze UI elements, buttons, forms, etc.
```

### 3. Check Analysis Status
```javascript
const analysisResponse = await fetch(`/file-upload/${fileId}/content-analysis`, {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const analysis = await analysisResponse.json();
console.log('Analysis status:', analysis.contentAnalysisStatus);
console.log('Description:', analysis.contentDescription);
```

## Benefits

### 1. Improved Test Case Quality
- **Visual Context**: Test cases now include UI interactions, visual validations
- **Workflow Understanding**: Better comprehension of process flows and user journeys
- **Error State Detection**: Identification of error screens and edge cases

### 2. Better Coverage
- **UI Testing**: Automated detection of buttons, forms, navigation elements
- **Visual Regression**: Understanding of layout and design elements
- **Accessibility**: Recognition of accessibility features and requirements

### 3. Enhanced Context
- **Rich Embeddings**: More contextual information for similarity search
- **Comprehensive Analysis**: Both textual and visual information preserved
- **Metadata Tracking**: Full audit trail of analysis process

## Performance Considerations

### 1. Processing Time
- Image analysis: 2-5 seconds per file
- Large PDFs: 5-15 seconds depending on content
- Batch processing: Configurable batch sizes to manage load

### 2. Token Usage
- Multimodal analysis uses more tokens than text-only
- Token usage tracked and recorded for billing
- Configurable timeouts to prevent runaway costs

### 3. Error Handling
- Graceful fallback to text-only analysis if vision fails
- Retry logic for transient failures
- Status tracking for monitoring and debugging

## Testing

Run the test suite:
```bash
npm test content-analysis.service.spec.ts
```

For integration tests with actual Gemini API:
```bash
npm run test:integration
```

## Troubleshooting

### Common Issues

1. **Analysis Status Stuck on 'pending'**
   - Check Gemini API key configuration
   - Verify Redis queue is running
   - Check worker process logs

2. **Large Files Timing Out**
   - Increase `CONTENT_ANALYSIS_TIMEOUT`
   - Check file size against `CONTENT_ANALYSIS_MAX_FILE_SIZE`
   - Consider file optimization

3. **Poor Analysis Quality**
   - Ensure images are high resolution
   - Check file format compatibility
   - Review prompt engineering in service

### Monitoring

- Check Bull dashboard at `/queues` for processing status
- Monitor token usage in database
- Review application logs for analysis errors
