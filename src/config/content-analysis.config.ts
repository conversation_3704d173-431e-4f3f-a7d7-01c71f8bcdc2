import { registerAs } from '@nestjs/config';

export interface ContentAnalysisConfig {
  enabled: boolean;
  defaultModel: string;
  maxFileSize: number; // in bytes
  supportedImageTypes: string[];
  supportedDocumentTypes: string[];
  analysisTimeout: number; // in milliseconds
  retryAttempts: number;
  batchSize: number;
}

export default registerAs('contentAnalysis', (): ContentAnalysisConfig => ({
  // Enable/disable content analysis feature
  enabled: process.env.CONTENT_ANALYSIS_ENABLED === 'true' || true,
  
  // Default model for content analysis
  defaultModel: process.env.CONTENT_ANALYSIS_MODEL || 'gemini-2.0-flash',
  
  // Maximum file size for content analysis (50MB default)
  maxFileSize: parseInt(process.env.CONTENT_ANALYSIS_MAX_FILE_SIZE || '52428800'),
  
  // Supported image types for multimodal analysis
  supportedImageTypes: [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff'
  ],
  
  // Supported document types that may contain images
  supportedDocumentTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  
  // Timeout for content analysis operations (5 minutes default)
  analysisTimeout: parseInt(process.env.CONTENT_ANALYSIS_TIMEOUT || '300000'),
  
  // Number of retry attempts for failed analysis
  retryAttempts: parseInt(process.env.CONTENT_ANALYSIS_RETRY_ATTEMPTS || '3'),
  
  // Batch size for processing multiple files
  batchSize: parseInt(process.env.CONTENT_ANALYSIS_BATCH_SIZE || '5')
}));
