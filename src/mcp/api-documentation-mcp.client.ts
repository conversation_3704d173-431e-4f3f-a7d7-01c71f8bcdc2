import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { spawn, ChildProcess } from 'child_process';

export interface McpToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface McpToolResult {
  content: Array<{
    type: string;
    text: string;
  }>;
}

@Injectable()
export class ApiDocumentationMcpClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ApiDocumentationMcpClient.name);
  private client: Client;
  private transport: StdioClientTransport;
  private serverProcess: ChildProcess;
  private isConnected = false;

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      // For now, we'll use the in-process server
      // In production, you might want to run the MCP server as a separate process
      this.logger.log('Initializing MCP client for API documentation');
      
      this.client = new Client(
        {
          name: 'api-documentation-client',
          version: '1.0.0',
        },
        {
          capabilities: {},
        }
      );

      // For in-process communication, we'll use a different approach
      // This is a simplified version - in production you'd use proper IPC
      this.isConnected = true;
      this.logger.log('MCP client connected');
    } catch (error) {
      this.logger.error('Failed to connect MCP client:', error);
      throw error;
    }
  }

  private async disconnect() {
    try {
      if (this.client && this.isConnected) {
        await this.client.close();
        this.isConnected = false;
      }
      
      if (this.serverProcess) {
        this.serverProcess.kill();
      }
      
      this.logger.log('MCP client disconnected');
    } catch (error) {
      this.logger.error('Error disconnecting MCP client:', error);
    }
  }

  async callTool(name: string, args: Record<string, any>): Promise<McpToolResult> {
    if (!this.isConnected) {
      throw new Error('MCP client not connected');
    }

    try {
      this.logger.debug(`Calling MCP tool: ${name} with args:`, args);
      
      // For now, we'll simulate the MCP call
      // In a real implementation, this would use the actual MCP protocol
      const result = await this.simulateToolCall(name, args);
      
      this.logger.debug(`MCP tool ${name} result:`, result);
      return result;
    } catch (error) {
      this.logger.error(`Error calling MCP tool ${name}:`, error);
      throw error;
    }
  }

  // Temporary simulation - replace with actual MCP calls
  private async simulateToolCall(name: string, args: Record<string, any>): Promise<McpToolResult> {
    // This is a placeholder that returns empty results
    // The actual implementation will be connected to the MCP server
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({ message: `Tool ${name} called with args`, args }, null, 2),
        },
      ],
    };
  }

  // Convenience methods for common API documentation operations
  async listApis(): Promise<any[]> {
    const result = await this.callTool('list_apis', {});
    return JSON.parse(result.content[0].text);
  }

  async getApiInfo(apiId: string): Promise<any> {
    const result = await this.callTool('get_api_info', { apiId });
    return JSON.parse(result.content[0].text);
  }

  async listEndpoints(apiId: string, tag?: string, method?: string): Promise<any[]> {
    const result = await this.callTool('list_endpoints', { apiId, tag, method });
    return JSON.parse(result.content[0].text);
  }

  async getEndpointDetails(apiId: string, path: string, method: string): Promise<any> {
    const result = await this.callTool('get_endpoint_details', { apiId, path, method });
    return JSON.parse(result.content[0].text);
  }

  async getSchemas(apiId: string, schemaName?: string): Promise<any> {
    const result = await this.callTool('get_schemas', { apiId, schemaName });
    return JSON.parse(result.content[0].text);
  }

  async getAuthentication(apiId: string): Promise<any> {
    const result = await this.callTool('get_authentication', { apiId });
    return JSON.parse(result.content[0].text);
  }

  async getEndpointsByTag(apiId: string): Promise<Record<string, any[]>> {
    const result = await this.callTool('get_endpoints_by_tag', { apiId });
    return JSON.parse(result.content[0].text);
  }

  async searchEndpoints(apiId: string, keyword: string): Promise<any[]> {
    const result = await this.callTool('search_endpoints', { apiId, keyword });
    return JSON.parse(result.content[0].text);
  }

  // Helper method to get comprehensive API structure for test generation
  async getApiStructureForTesting(apiId: string): Promise<{
    apiInfo: any;
    endpoints: any[];
    schemas: any;
    authentication: any;
    endpointsByTag: Record<string, any[]>;
  }> {
    try {
      const [apiInfo, endpoints, schemas, authentication, endpointsByTag] = await Promise.all([
        this.getApiInfo(apiId),
        this.listEndpoints(apiId),
        this.getSchemas(apiId),
        this.getAuthentication(apiId),
        this.getEndpointsByTag(apiId),
      ]);

      return {
        apiInfo,
        endpoints,
        schemas,
        authentication,
        endpointsByTag,
      };
    } catch (error) {
      this.logger.error(`Error getting API structure for ${apiId}:`, error);
      throw error;
    }
  }

  // Method to get detailed information for specific endpoints
  async getEndpointsWithDetails(apiId: string, endpointPaths: Array<{ path: string; method: string }>): Promise<any[]> {
    try {
      const detailedEndpoints = await Promise.all(
        endpointPaths.map(async ({ path, method }) => {
          try {
            return await this.getEndpointDetails(apiId, path, method);
          } catch (error) {
            this.logger.warn(`Failed to get details for ${method} ${path}:`, error);
            return null;
          }
        })
      );

      return detailedEndpoints.filter(endpoint => endpoint !== null);
    } catch (error) {
      this.logger.error(`Error getting endpoint details for ${apiId}:`, error);
      throw error;
    }
  }

  // Method to get endpoints by priority for test generation
  async getEndpointsByPriority(apiId: string): Promise<{
    critical: any[];
    high: any[];
    medium: any[];
    low: any[];
  }> {
    try {
      const endpoints = await this.listEndpoints(apiId);
      
      // Categorize endpoints by priority based on HTTP methods and common patterns
      const critical = endpoints.filter(ep => 
        ep.method === 'POST' && (ep.path.includes('/auth') || ep.path.includes('/login'))
      );
      
      const high = endpoints.filter(ep => 
        ['POST', 'PUT', 'DELETE'].includes(ep.method) && !critical.includes(ep)
      );
      
      const medium = endpoints.filter(ep => 
        ep.method === 'GET' && (ep.path.includes('/list') || ep.path.includes('/search'))
      );
      
      const low = endpoints.filter(ep => 
        !critical.includes(ep) && !high.includes(ep) && !medium.includes(ep)
      );

      return { critical, high, medium, low };
    } catch (error) {
      this.logger.error(`Error categorizing endpoints by priority for ${apiId}:`, error);
      throw error;
    }
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }
      
      // Try a simple operation to check if the connection is working
      await this.listApis();
      return true;
    } catch (error) {
      this.logger.warn('MCP client health check failed:', error);
      return false;
    }
  }
}
