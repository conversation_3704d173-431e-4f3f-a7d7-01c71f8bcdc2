import { Module } from '@nestjs/common';
import { ApiDocumentationMcpServer } from './api-documentation-mcp.server';
import { ApiDocumentationMcpClient } from './api-documentation-mcp.client';
import { ApiDocumentationMcpService } from './api-documentation-mcp.service';

@Module({
  providers: [
    ApiDocumentationMcpServer,
    ApiDocumentationMcpClient,
    ApiDocumentationMcpService,
  ],
  exports: [
    ApiDocumentationMcpService,
    ApiDocumentationMcpClient,
  ],
})
export class McpModule {}
