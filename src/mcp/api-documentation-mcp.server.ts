import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import { Injectable, Logger } from '@nestjs/common';

export interface ApiEndpoint {
  path: string;
  method: string;
  summary?: string;
  description?: string;
  parameters?: ApiParameter[];
  requestBody?: any;
  responses?: any;
  tags?: string[];
  operationId?: string;
}

export interface ApiParameter {
  name: string;
  in: 'query' | 'path' | 'header' | 'cookie' | 'body';
  type: string;
  required: boolean;
  description?: string;
  example?: any;
  schema?: any;
}

export interface ApiSchema {
  name: string;
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  description?: string;
}

export interface ApiDocumentationData {
  title: string;
  description?: string;
  version?: string;
  baseUrl?: string;
  endpoints: ApiEndpoint[];
  schemas: Record<string, ApiSchema>;
  tags: string[];
  servers: any[];
  authentication?: any;
  metadata: any;
}

@Injectable()
export class ApiDocumentationMcpServer {
  private readonly logger = new Logger(ApiDocumentationMcpServer.name);
  private server: Server;
  private apiData: Map<string, ApiDocumentationData> = new Map();

  constructor() {
    this.server = new Server(
      {
        name: 'api-documentation-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupTools();
  }

  private setupTools() {
    // List all available API documentation
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'list_apis',
            description: 'List all available API documentation',
            inputSchema: {
              type: 'object',
              properties: {},
            },
          },
          {
            name: 'get_api_info',
            description: 'Get basic information about an API',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
              },
              required: ['apiId'],
            },
          },
          {
            name: 'list_endpoints',
            description: 'List all endpoints for an API',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
                tag: {
                  type: 'string',
                  description: 'Filter by tag (optional)',
                },
                method: {
                  type: 'string',
                  description: 'Filter by HTTP method (optional)',
                },
              },
              required: ['apiId'],
            },
          },
          {
            name: 'get_endpoint_details',
            description: 'Get detailed information about a specific endpoint',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
                path: {
                  type: 'string',
                  description: 'The endpoint path',
                },
                method: {
                  type: 'string',
                  description: 'The HTTP method',
                },
              },
              required: ['apiId', 'path', 'method'],
            },
          },
          {
            name: 'get_schemas',
            description: 'Get all schemas/models for an API',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
                schemaName: {
                  type: 'string',
                  description: 'Specific schema name (optional)',
                },
              },
              required: ['apiId'],
            },
          },
          {
            name: 'get_authentication',
            description: 'Get authentication information for an API',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
              },
              required: ['apiId'],
            },
          },
          {
            name: 'get_endpoints_by_tag',
            description: 'Get all endpoints grouped by tags',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
              },
              required: ['apiId'],
            },
          },
          {
            name: 'search_endpoints',
            description: 'Search endpoints by keyword',
            inputSchema: {
              type: 'object',
              properties: {
                apiId: {
                  type: 'string',
                  description: 'The API identifier',
                },
                keyword: {
                  type: 'string',
                  description: 'Search keyword',
                },
              },
              required: ['apiId', 'keyword'],
            },
          },
        ] as Tool[],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'list_apis':
            return this.listApis();

          case 'get_api_info':
            return this.getApiInfo(args.apiId as string);

          case 'list_endpoints':
            return this.listEndpoints(args.apiId as string, args.tag as string, args.method as string);

          case 'get_endpoint_details':
            return this.getEndpointDetails(args.apiId as string, args.path as string, args.method as string);

          case 'get_schemas':
            return this.getSchemas(args.apiId as string, args.schemaName as string);

          case 'get_authentication':
            return this.getAuthentication(args.apiId as string);

          case 'get_endpoints_by_tag':
            return this.getEndpointsByTag(args.apiId as string);

          case 'search_endpoints':
            return this.searchEndpoints(args.apiId as string, args.keyword as string);

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        this.logger.error(`Error executing tool ${name}:`, error);
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
        };
      }
    });
  }

  // Store API documentation data
  async storeApiDocumentation(apiId: string, data: ApiDocumentationData) {
    this.apiData.set(apiId, data);
    this.logger.log(`Stored API documentation for ${apiId} with ${data.endpoints.length} endpoints`);
  }

  // Remove API documentation data
  async removeApiDocumentation(apiId: string) {
    this.apiData.delete(apiId);
    this.logger.log(`Removed API documentation for ${apiId}`);
  }

  // Tool implementations
  private async listApis() {
    const apis = Array.from(this.apiData.entries()).map(([id, data]) => ({
      id,
      title: data.title,
      version: data.version,
      endpointCount: data.endpoints.length,
      baseUrl: data.baseUrl,
    }));

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(apis, null, 2),
        },
      ],
    };
  }

  private async getApiInfo(apiId: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    const info = {
      title: data.title,
      description: data.description,
      version: data.version,
      baseUrl: data.baseUrl,
      endpointCount: data.endpoints.length,
      schemaCount: Object.keys(data.schemas).length,
      tags: data.tags,
      servers: data.servers,
    };

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(info, null, 2),
        },
      ],
    };
  }

  private async listEndpoints(apiId: string, tag?: string, method?: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    let endpoints = data.endpoints;

    // Filter by tag if provided
    if (tag) {
      endpoints = endpoints.filter(ep => ep.tags?.includes(tag));
    }

    // Filter by method if provided
    if (method) {
      endpoints = endpoints.filter(ep => ep.method.toLowerCase() === method.toLowerCase());
    }

    const endpointList = endpoints.map(ep => ({
      path: ep.path,
      method: ep.method,
      summary: ep.summary,
      tags: ep.tags,
      operationId: ep.operationId,
    }));

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(endpointList, null, 2),
        },
      ],
    };
  }

  private async getEndpointDetails(apiId: string, path: string, method: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    const endpoint = data.endpoints.find(
      ep => ep.path === path && ep.method.toLowerCase() === method.toLowerCase()
    );

    if (!endpoint) {
      throw new Error(`Endpoint ${method} ${path} not found`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(endpoint, null, 2),
        },
      ],
    };
  }

  private async getSchemas(apiId: string, schemaName?: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    if (schemaName) {
      const schema = data.schemas[schemaName];
      if (!schema) {
        throw new Error(`Schema ${schemaName} not found`);
      }
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(schema, null, 2),
          },
        ],
      };
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data.schemas, null, 2),
        },
      ],
    };
  }

  private async getAuthentication(apiId: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(data.authentication || {}, null, 2),
        },
      ],
    };
  }

  private async getEndpointsByTag(apiId: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    const endpointsByTag: Record<string, any[]> = {};
    
    data.endpoints.forEach(endpoint => {
      const tags = endpoint.tags || ['untagged'];
      tags.forEach(tag => {
        if (!endpointsByTag[tag]) {
          endpointsByTag[tag] = [];
        }
        endpointsByTag[tag].push({
          path: endpoint.path,
          method: endpoint.method,
          summary: endpoint.summary,
        });
      });
    });

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(endpointsByTag, null, 2),
        },
      ],
    };
  }

  private async searchEndpoints(apiId: string, keyword: string) {
    const data = this.apiData.get(apiId);
    if (!data) {
      throw new Error(`API ${apiId} not found`);
    }

    const searchTerm = keyword.toLowerCase();
    const matchingEndpoints = data.endpoints.filter(endpoint => 
      endpoint.path.toLowerCase().includes(searchTerm) ||
      endpoint.summary?.toLowerCase().includes(searchTerm) ||
      endpoint.description?.toLowerCase().includes(searchTerm) ||
      endpoint.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    );

    const results = matchingEndpoints.map(ep => ({
      path: ep.path,
      method: ep.method,
      summary: ep.summary,
      tags: ep.tags,
    }));

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(results, null, 2),
        },
      ],
    };
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    this.logger.log('API Documentation MCP Server started');
  }

  async stop() {
    await this.server.close();
    this.logger.log('API Documentation MCP Server stopped');
  }
}
