import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ApiDocumentationMcpServer, ApiDocumentationData, ApiEndpoint, ApiSchema } from './api-documentation-mcp.server';
import { ApiDocumentationMcpClient } from './api-documentation-mcp.client';
import { ParsedApiDocumentation } from '../publish-url/api-documentation-parser.service';

@Injectable()
export class ApiDocumentationMcpService implements OnModuleInit {
  private readonly logger = new Logger(ApiDocumentationMcpService.name);
  private mcpServer: ApiDocumentationMcpServer;
  private mcpClient: ApiDocumentationMcpClient;

  constructor() {
    this.mcpServer = new ApiDocumentationMcpServer();
    this.mcpClient = new ApiDocumentationMcpClient();
  }

  async onModuleInit() {
    try {
      // Start the MCP server
      // Note: In production, you might want to run this as a separate process
      // For now, we'll keep it in-process for simplicity
      this.logger.log('Initializing API Documentation MCP Service');
    } catch (error) {
      this.logger.error('Failed to initialize MCP service:', error);
    }
  }

  /**
   * Store parsed API documentation in the MCP server
   */
  async storeApiDocumentation(apiId: string, parsedApi: ParsedApiDocumentation): Promise<void> {
    try {
      // Convert parsed API documentation to MCP format
      const mcpData: ApiDocumentationData = {
        title: parsedApi.title,
        description: parsedApi.description,
        version: parsedApi.version,
        baseUrl: parsedApi.baseUrl,
        endpoints: parsedApi.endpoints.map((endpoint: any) => this.convertEndpointToMcp(endpoint)),
        schemas: this.convertSchemasToMcp(parsedApi.schemas),
        tags: parsedApi.tags || [],
        servers: parsedApi.servers || [],
        authentication: this.extractAuthentication(parsedApi),
        metadata: parsedApi.metadata,
      };

      await this.mcpServer.storeApiDocumentation(apiId, mcpData);
      this.logger.log(`Stored API documentation for ${apiId} in MCP server`);
    } catch (error) {
      this.logger.error(`Failed to store API documentation for ${apiId}:`, error);
      throw error;
    }
  }

  /**
   * Remove API documentation from the MCP server
   */
  async removeApiDocumentation(apiId: string): Promise<void> {
    try {
      await this.mcpServer.removeApiDocumentation(apiId);
      this.logger.log(`Removed API documentation for ${apiId} from MCP server`);
    } catch (error) {
      this.logger.error(`Failed to remove API documentation for ${apiId}:`, error);
      throw error;
    }
  }

  /**
   * Get the MCP client for making tool calls
   */
  getMcpClient(): ApiDocumentationMcpClient {
    return this.mcpClient;
  }

  /**
   * Get comprehensive API structure for test generation
   */
  async getApiStructureForTesting(apiId: string) {
    return this.mcpClient.getApiStructureForTesting(apiId);
  }

  /**
   * Get endpoints categorized by priority for intelligent test generation
   */
  async getEndpointsByPriority(apiId: string) {
    return this.mcpClient.getEndpointsByPriority(apiId);
  }

  /**
   * Search for specific endpoints by keyword
   */
  async searchEndpoints(apiId: string, keyword: string) {
    return this.mcpClient.searchEndpoints(apiId, keyword);
  }

  /**
   * Get detailed information for specific endpoints
   */
  async getEndpointsWithDetails(apiId: string, endpointPaths: Array<{ path: string; method: string }>) {
    return this.mcpClient.getEndpointsWithDetails(apiId, endpointPaths);
  }

  // Private helper methods for data conversion

  private convertEndpointToMcp(endpoint: any): ApiEndpoint {
    return {
      path: endpoint.path,
      method: endpoint.method,
      summary: endpoint.summary,
      description: endpoint.description,
      parameters: endpoint.parameters?.map((param: any) => this.convertParameterToMcp(param)) || [],
      requestBody: endpoint.requestBody,
      responses: endpoint.responses,
      tags: endpoint.tags || [],
      operationId: endpoint.operationId,
    };
  }

  private convertParameterToMcp(param: any) {
    return {
      name: param.name,
      in: param.in,
      type: param.type,
      required: param.required || false,
      description: param.description,
      example: param.example,
      schema: param.schema,
    };
  }

  private convertSchemasToMcp(schemas: any): Record<string, ApiSchema> {
    if (!schemas || typeof schemas !== 'object') {
      return {};
    }

    const mcpSchemas: Record<string, ApiSchema> = {};
    
    for (const [name, schema] of Object.entries(schemas)) {
      if (schema && typeof schema === 'object') {
        const schemaObj = schema as any;
        mcpSchemas[name] = {
          name,
          type: schemaObj.type || 'object',
          properties: schemaObj.properties,
          required: schemaObj.required,
          description: schemaObj.description,
        };
      }
    }

    return mcpSchemas;
  }

  private extractAuthentication(parsedApi: ParsedApiDocumentation): any {
    // Extract authentication information from the parsed API
    // This could come from security schemes, components, etc.
    const auth: any = {};

    // Check for common authentication patterns
    if (parsedApi.metadata?.spec) {
      const spec = parsedApi.metadata.spec;
      
      // OpenAPI 3.x security schemes
      if (spec.components?.securitySchemes) {
        auth.securitySchemes = spec.components.securitySchemes;
      }
      
      // Swagger 2.0 security definitions
      if (spec.securityDefinitions) {
        auth.securityDefinitions = spec.securityDefinitions;
      }
      
      // Global security requirements
      if (spec.security) {
        auth.security = spec.security;
      }
    }

    return auth;
  }

  /**
   * Generate a unique API ID from URL
   */
  generateApiId(url: string): string {
    // Create a consistent ID from the URL
    const urlObj = new URL(url);
    const host = urlObj.hostname.replace(/\./g, '_');
    const path = urlObj.pathname.replace(/[^a-zA-Z0-9]/g, '_');
    return `${host}${path}`.toLowerCase();
  }

  /**
   * Check if API documentation exists in MCP server
   */
  async hasApiDocumentation(apiId: string): Promise<boolean> {
    try {
      await this.mcpClient.getApiInfo(apiId);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get statistics about stored API documentation
   */
  async getApiStatistics(apiId: string): Promise<{
    endpointCount: number;
    schemaCount: number;
    tagCount: number;
    methodDistribution: Record<string, number>;
  }> {
    try {
      const [apiInfo, endpoints] = await Promise.all([
        this.mcpClient.getApiInfo(apiId),
        this.mcpClient.listEndpoints(apiId),
      ]);

      // Calculate method distribution
      const methodDistribution: Record<string, number> = {};
      endpoints.forEach(endpoint => {
        const method = endpoint.method.toUpperCase();
        methodDistribution[method] = (methodDistribution[method] || 0) + 1;
      });

      return {
        endpointCount: apiInfo.endpointCount,
        schemaCount: apiInfo.schemaCount,
        tagCount: apiInfo.tags?.length || 0,
        methodDistribution,
      };
    } catch (error) {
      this.logger.error(`Failed to get API statistics for ${apiId}:`, error);
      throw error;
    }
  }

  /**
   * Validate API documentation completeness
   */
  async validateApiDocumentation(apiId: string): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const [apiInfo, endpoints, schemas] = await Promise.all([
        this.mcpClient.getApiInfo(apiId),
        this.mcpClient.listEndpoints(apiId),
        this.mcpClient.getSchemas(apiId),
      ]);

      const issues: string[] = [];
      const recommendations: string[] = [];

      // Check for missing descriptions
      if (!apiInfo.description) {
        issues.push('API description is missing');
      }

      // Check endpoints without descriptions
      const endpointsWithoutDesc = endpoints.filter(ep => !ep.summary && !ep.description);
      if (endpointsWithoutDesc.length > 0) {
        issues.push(`${endpointsWithoutDesc.length} endpoints missing descriptions`);
      }

      // Check for authentication documentation
      const auth = await this.mcpClient.getAuthentication(apiId);
      if (!auth || Object.keys(auth).length === 0) {
        recommendations.push('Consider documenting authentication methods');
      }

      // Check for examples
      const endpointsWithDetails = await Promise.all(
        endpoints.slice(0, 5).map(ep => 
          this.mcpClient.getEndpointDetails(apiId, ep.path, ep.method).catch(() => null)
        )
      );

      const endpointsWithoutExamples = endpointsWithDetails.filter(ep => 
        ep && (!ep.parameters?.some((p: any) => p.example) && !ep.requestBody?.example)
      );

      if (endpointsWithoutExamples.length > 0) {
        recommendations.push('Add examples to endpoint parameters and request bodies');
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      this.logger.error(`Failed to validate API documentation for ${apiId}:`, error);
      return {
        isValid: false,
        issues: [`Validation failed: ${error.message}`],
        recommendations: [],
      };
    }
  }
}
