import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddContentAnalysisFields1703000000000 implements MigrationInterface {
  name = 'AddContentAnalysisFields1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add content analysis fields to file_uploads table
    await queryRunner.addColumns('file_uploads', [
      new TableColumn({
        name: 'contentDescription',
        type: 'text',
        isNullable: true,
        comment: 'LLM-generated content description for multimodal analysis'
      }),
      new TableColumn({
        name: 'contentAnalysisMetadata',
        type: 'json',
        isNullable: true,
        comment: 'Metadata about content analysis including model used and analysis type'
      }),
      new TableColumn({
        name: 'contentAnalysisStatus',
        type: 'varchar',
        length: '50',
        isNullable: true,
        default: "'pending'",
        comment: 'Status of content analysis processing'
      })
    ]);

    // Create index on contentAnalysisStatus for efficient querying
    await queryRunner.query(`
      CREATE INDEX "IDX_file_uploads_content_analysis_status" 
      ON "file_uploads" ("contentAnalysisStatus")
    `);

    // Create index on contentDescription for text search (if using PostgreSQL)
    await queryRunner.query(`
      CREATE INDEX "IDX_file_uploads_content_description_gin" 
      ON "file_uploads" USING gin(to_tsvector('english', "contentDescription"))
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_file_uploads_content_description_gin"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_file_uploads_content_analysis_status"`);

    // Drop columns
    await queryRunner.dropColumns('file_uploads', [
      'contentDescription',
      'contentAnalysisMetadata', 
      'contentAnalysisStatus'
    ]);
  }
}
