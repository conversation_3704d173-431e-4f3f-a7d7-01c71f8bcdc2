import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class GoogleStorageService {
  private storage: Storage;
  private bucket: string;

  constructor(private configService: ConfigService) {
    this.storage = new Storage({
      projectId: this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID'),
      credentials: {
        client_email: this.configService.get<string>('GOOGLE_CLOUD_CLIENT_EMAIL'),
        private_key: this.configService.get<string>('GOOGLE_CLOUD_PRIVATE_KEY').replace(/\\n/g, '\n'),
      },
    });
    this.bucket = this.configService.get<string>('GOOGLE_CLOUD_BUCKET');
  }

  async uploadFile(file: Express.Multer.File): Promise<string> {
    const bucket = this.storage.bucket(this.bucket);
    const fileName = `testcase_artifact/${uuidv4()}-${file.originalname}`;
    const blob = bucket.file(fileName);
    
    const blobStream = blob.createWriteStream({
      resumable: false,
      metadata: {
        contentType: file.mimetype,
      },
    });

    return new Promise((resolve, reject) => {
      blobStream.on('error', (error) => reject(error));
      blobStream.on('finish', () => {
        const publicUrl = `https://storage.googleapis.com/${this.bucket}/${fileName}`;
        resolve(publicUrl);
      });
      blobStream.end(file.buffer);
    });
  }

  async deleteFile(url: string): Promise<void> {
    // Extract the file name with the 'testcase_artifact/' prefix
    const fileName = url.substring(url.indexOf('testcase_artifact/')); 
  
    const bucket = this.storage.bucket(this.bucket);
    const file = bucket.file(fileName);
    await file.delete();
  }  
}