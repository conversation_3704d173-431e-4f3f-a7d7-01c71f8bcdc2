import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { FileUpload } from './file-upload.entity';
import { PublishUrl } from '../publish-url/publish-url.entity';

@Entity('embeddings')
export class Embedding {
  @ApiProperty({
    description: 'The unique identifier of the embedding',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The text chunk that was embedded',
    example: 'This is a section of the document...'
  })
  @Column('text')
  content: string;

  @ApiProperty({
    description: 'The OpenAI embedding vector',
    example: '[0.1, 0.2, 0.3, ...]'
  })
  @Column('float', { array: true })
  embedding: number[];

  @ApiProperty({
    description: 'The file upload this embedding belongs to',
    type: () => FileUpload
  })
  @ManyToOne(() => FileUpload, { onDelete: 'CASCADE' })
  fileUpload: FileUpload;

  @ApiProperty({
    description: 'The ID of the file upload',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  fileUploadId: string;

  @ApiProperty({
    description: 'The publish URL this embedding belongs to',
    type: () => PublishUrl
  })
  @ManyToOne(() => PublishUrl, { onDelete: 'CASCADE', nullable: true })
  publishUrl: PublishUrl;

  @ApiProperty({
    description: 'The ID of the publish URL',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  publishUrlId: string;

  @ApiProperty({
    description: 'When the embedding was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;
}