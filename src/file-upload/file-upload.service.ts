import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { FileUpload } from './file-upload.entity';
import { Embedding } from './embedding.entity';
import { TokenUsage, TokenType } from './token-usage.entity';
import { GoogleStorageService } from './google.storage.service';
import { LLMService } from './openai.service';
import { TokenUsageResponseDto, TokenUsageDetailDto } from './dto/token-usage-response.dto';
import { TestCaseGeneration } from './test-case-generation.entity';

@Injectable()
export class FileUploadService {
  constructor(
    @InjectRepository(FileUpload)
    private fileUploadRepository: Repository<FileUpload>,
    @InjectRepository(Embedding)
    private embeddingRepository: Repository<Embedding>,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
    @InjectRepository(TestCaseGeneration)
    private testCaseGenerationRepository: Repository<TestCaseGeneration>,
    @InjectQueue('embeddings')
    private embeddingsQueue: Queue,
    private googleStorageService: GoogleStorageService,
    private openAIService: LLMService,
  ) {}

  async uploadFile(file: Express.Multer.File): Promise<FileUpload> {
    // Upload to Google Cloud Storage
    const url = await this.googleStorageService.uploadFile(file);

    // Create database record
    const fileUpload = await this.fileUploadRepository.save({
      filename: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url,
    });

    // Add embedding generation job to queue
    await this.embeddingsQueue.add('generate', {
      fileId: fileUpload.id,
      fileBuffer: file.buffer,
      mimetype: file.mimetype,
      filename: file.originalname,
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: true,
    });

    return fileUpload;
  }

  async findAll(): Promise<FileUpload[]> {
    return this.fileUploadRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<FileUpload> {
    const file = await this.fileUploadRepository.findOne({
      where: { id },
    });

    if (!file) {
      throw new NotFoundException('File not found');
    }

    return file;
  }

  async remove(id: string): Promise<void> {
    const file = await this.findOne(id);
    
    // Delete from Google Cloud Storage
    await this.googleStorageService.deleteFile(file.url);
    
    // Delete database record (embeddings will be deleted automatically due to CASCADE)
    await this.fileUploadRepository.remove(file);
  }

  async getEmbeddings(fileUploadId: string): Promise<Embedding[]> {
    return this.embeddingRepository.find({
      where: { fileUploadId },
      order: { createdAt: 'ASC' },
    });
  }

  async getEmbeddingProgress(fileId: string): Promise<number> {
    // First verify file exists
    await this.findOne(fileId);

    // Get active and waiting jobs
    const jobs = await this.embeddingsQueue.getJobs(['active', 'waiting']);
    const job = jobs.find(j => j.data.fileId === fileId);
    
    if (!job) {
      // If no job found, check if we have any embeddings
      const embeddings = await this.embeddingRepository.count({
        where: { fileUploadId: fileId },
      });
      
      return embeddings > 0 ? 100 : 0;
    }

    // Get job state and data
    const state = await job.getState();
    const data = job.data;

    if (state === 'completed') {
      return 100;
    }

    if (state === 'failed') {
      return 0;
    }

    // Get progress from job data
    const progress = data.progress || 0;
    return progress;
  }

  async generateTestCases(fileId: string) {
    // First verify file exists
    const file = await this.findOne(fileId);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Get embeddings for the file
    const embeddings = await this.embeddingRepository.find({
      where: { fileUploadId: fileId },
      order: { createdAt: 'ASC' }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this file');
    }

    // Combine all content
    const combinedContent = embeddings.map(e => e.content).join('\n\n');

    // Generate test cases using OpenAI
    const generatedTestCases = await this.openAIService.generateTestCases(combinedContent, embeddings[0].id);

    // Store the generated test cases
    const testCases = generatedTestCases.map(testCase => 
      this.testCaseGenerationRepository.create({
        ...testCase,
        fileUploadId: fileId
      })
    );

    // Save all test cases
    await this.testCaseGenerationRepository.save(testCases);

    return testCases;
  }

  async generateMoreTestCases(fileId: string) {
    // First verify file exists
    const file = await this.findOne(fileId);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Get existing test cases to avoid duplicates
    const existingTestCases = await this.testCaseGenerationRepository.find({
      where: { fileUploadId: fileId }
    });

    // Get embeddings for the file
    const embeddings = await this.embeddingRepository.find({
      where: { fileUploadId: fileId },
      order: { createdAt: 'ASC' }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this file');
    }

    // Combine all content
    const combinedContent = embeddings.map(e => e.content).join('\n\n');

    // Generate additional test cases using OpenAI with context about existing ones
    const existingTestNames = existingTestCases.map(tc => tc.name);
    const existingScenarios = existingTestCases.map(tc => ({
      name: tc.name,
      type: tc.testType,
      platform: tc.platform,
      automationByAgentq: tc.automationByAgentq
    }));

    // Generate new test cases with context about existing ones
    const generatedTestCases = await this.openAIService.generateMoreTestCases(
      combinedContent,
      embeddings[0].id,
      existingScenarios
    );

    // Store the generated test cases
    const testCases = generatedTestCases.map(testCase => 
      this.testCaseGenerationRepository.create({
        ...testCase,
        fileUploadId: fileId
      })
    );

    // Save all test cases
    await this.testCaseGenerationRepository.save(testCases);

    return testCases;
  }

  async getGeneratedTestCases(fileId: string) {
    // First verify file exists
    const file = await this.findOne(fileId);
    if (!file) {
      throw new NotFoundException('File not found');
    }

    // Get all test cases for this file
    const testCases = await this.testCaseGenerationRepository.find({
      where: { fileUploadId: fileId },
      order: { createdAt: 'ASC' }
    });

    if (!testCases.length) {
      throw new NotFoundException('No generated test cases found for this file');
    }

    return testCases;
  }

  async getEmbeddingUsage(fileId: string): Promise<TokenUsageResponseDto> {
    // Verify file exists
    const file = await this.findOne(fileId);

    // Get all embeddings for this file
    const embeddings = await this.embeddingRepository.find({
      where: { fileUploadId: fileId }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this file');
    }

    // Get all token usage records for these embeddings
    const embeddingIds = embeddings.map(e => e.id);
    const tokenUsages = await this.tokenUsageRepository.find({
      where: {
        embeddingId: In(embeddingIds),
        tokenType: TokenType.EMBEDDING_INPUT
      },
      order: { timestamp: 'ASC' }
    });

    if (!tokenUsages.length) {
      throw new NotFoundException('No embedding token usage data found for this file');
    }

    const totalTokens = tokenUsages.reduce((sum, u) => sum + u.tokensUsed, 0);

    return {
      fileId,
      totalTokensUsed: totalTokens,
      usageByType: [{
        tokenType: TokenType.EMBEDDING_INPUT,
        totalTokens,
        operationCount: tokenUsages.length,
        averageTokensPerOperation: Math.round(totalTokens / tokenUsages.length)
      }],
      firstUsageTime: tokenUsages[0].timestamp,
      lastUsageTime: tokenUsages[tokenUsages.length - 1].timestamp
    };
  }

  async getCompletionUsage(fileId: string): Promise<TokenUsageResponseDto> {
    // Verify file exists
    const file = await this.findOne(fileId);

    // Get all embeddings for this file
    const embeddings = await this.embeddingRepository.find({
      where: { fileUploadId: fileId }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this file');
    }

    // Get all token usage records for these embeddings
    const embeddingIds = embeddings.map(e => e.id);
    const tokenUsages = await this.tokenUsageRepository.find({
      where: {
        embeddingId: In(embeddingIds),
        tokenType: In([TokenType.COMPLETION_INPUT, TokenType.COMPLETION_OUTPUT])
      },
      order: { timestamp: 'ASC' }
    });

    if (!tokenUsages.length) {
      throw new NotFoundException('No completion token usage data found for this file');
    }

    // Calculate usage statistics by token type
    const usageByType = [TokenType.COMPLETION_INPUT, TokenType.COMPLETION_OUTPUT].map(tokenType => {
      const usagesOfType = tokenUsages.filter(u => u.tokenType === tokenType);
      const totalTokens = usagesOfType.reduce((sum, u) => sum + u.tokensUsed, 0);
      
      return {
        tokenType,
        totalTokens,
        operationCount: usagesOfType.length,
        averageTokensPerOperation: usagesOfType.length ? Math.round(totalTokens / usagesOfType.length) : 0
      };
    }).filter(usage => usage.operationCount > 0);

    return {
      fileId,
      totalTokensUsed: tokenUsages.reduce((sum, u) => sum + u.tokensUsed, 0),
      usageByType,
      firstUsageTime: tokenUsages[0].timestamp,
      lastUsageTime: tokenUsages[tokenUsages.length - 1].timestamp
    };
  }

  async updateGeneratedTestCase(fileUploadId: string, id: string, updateData: Partial<TestCaseGeneration>) {
    // First verify file exists
    await this.findOne(fileUploadId);

    // Find the test case
    const testCase = await this.testCaseGenerationRepository.findOne({
      where: { id, fileUploadId }
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Update the test case
    Object.assign(testCase, updateData);
    return this.testCaseGenerationRepository.save(testCase);
  }

  async deleteGeneratedTestCase(fileUploadId: string, id: string) {
    // First verify file exists
    await this.findOne(fileUploadId);

    // Find the test case
    const testCase = await this.testCaseGenerationRepository.findOne({
      where: { id, fileUploadId }
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Delete the test case
    await this.testCaseGenerationRepository.remove(testCase);
  }
}