import { Injectable, Logger } from '@nestjs/common';
import * as pdfjsLib from 'pdf-parse';
import * as mammoth from 'mammoth';
import { ContentAnalysisService, ContentAnalysisResult } from './content-analysis.service';

export interface EnhancedTextExtractionResult {
  text: string;
  contentAnalysis?: ContentAnalysisResult;
  requiresMultimodalAnalysis: boolean;
}

@Injectable()
export class FileProcessorService {
  private readonly logger = new Logger(FileProcessorService.name);

  constructor(
    private contentAnalysisService: ContentAnalysisService
  ) {}

  /**
   * Extract text with optional content analysis for enhanced context
   */
  async extractTextWithAnalysis(
    file: Express.Multer.File,
    fileId: string,
    enableContentAnalysis: boolean = true
  ): Promise<EnhancedTextExtractionResult> {
    const text = await this.extractText(file);
    const requiresMultimodalAnalysis = this.requiresMultimodalAnalysis(file.mimetype);

    let contentAnalysis: ContentAnalysisResult | undefined;

    if (enableContentAnalysis && requiresMultimodalAnalysis) {
      try {
        contentAnalysis = await this.contentAnalysisService.analyzeFileContent(file, fileId);
        this.logger.log(`Content analysis completed for ${file.originalname}: ${contentAnalysis.metadata.analysisType}`);
      } catch (error) {
        this.logger.error(`Content analysis failed for ${file.originalname}: ${error.message}`);
        // Continue without content analysis if it fails
      }
    }

    return {
      text,
      contentAnalysis,
      requiresMultimodalAnalysis
    };
  }

  /**
   * Original text extraction method (maintained for backward compatibility)
   */
  async extractText(file: Express.Multer.File): Promise<string> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.extractFromPDF(file.buffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractFromDOCX(file.buffer);
      case 'text/plain':
      case 'text/markdown':
      case 'text/html':
      case 'text/csv':
        return this.extractFromText(file.buffer);
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
      case 'image/webp':
      case 'image/bmp':
      case 'image/tiff':
        return this.extractFromImage(file.buffer, file.mimetype);
      default:
        throw new Error(`Unsupported file type: ${file.mimetype}`);
    }
  }

  private async extractFromPDF(buffer: Buffer): Promise<string> {
    const data = await pdfjsLib(buffer);
    return data.text;
  }

  private async extractFromDOCX(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  private async extractFromText(buffer: Buffer): Promise<string> {
    return buffer.toString('utf-8');
  }

  private async extractFromImage(buffer: Buffer, mimetype: string): Promise<string> {
    // For image files, we return a placeholder text since the actual content analysis
    // will be done by the ContentAnalysisService using Gemini Vision
    return `[IMAGE FILE: ${mimetype}] This is an image file that will be analyzed using multimodal content analysis.`;
  }

  /**
   * Enhanced chunking that includes content analysis descriptions
   */
  splitIntoChunksWithAnalysis(
    text: string,
    contentAnalysis?: ContentAnalysisResult,
    maxChunkSize: number = 1000
  ): string[] {
    let enhancedText = text;

    // Prepend content analysis description to provide context for embeddings
    if (contentAnalysis?.description) {
      const analysisPrefix = `[CONTENT ANALYSIS]: ${contentAnalysis.description}\n\n[ORIGINAL CONTENT]: `;
      enhancedText = analysisPrefix + text;
    }

    return this.splitIntoChunks(enhancedText, maxChunkSize);
  }

  /**
   * Original chunking method (maintained for backward compatibility)
   */
  splitIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
    const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= maxChunkSize) {
        currentChunk += sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk.trim());
        currentChunk = sentence;
      }
    }

    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks;
  }

  /**
   * Check if file type requires multimodal analysis
   */
  private requiresMultimodalAnalysis(mimetype: string): boolean {
    const multimodalTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/bmp',
      'image/tiff'
    ];
    return multimodalTypes.includes(mimetype);
  }
}