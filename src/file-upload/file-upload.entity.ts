import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('file_uploads')
export class FileUpload {
  @ApiProperty({
    description: 'The unique identifier of the file upload',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Original filename',
    example: 'document.pdf'
  })
  @Column()
  filename: string;

  @ApiProperty({
    description: 'File MIME type',
    example: 'application/pdf'
  })
  @Column()
  mimetype: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL',
    example: 'https://storage.googleapis.com/bucket-name/path/to/file.pdf'
  })
  @Column()
  url: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024
  })
  @Column()
  size: number;

  @ApiProperty({
    description: 'LLM-generated content description for multimodal analysis',
    example: 'This document contains a flowchart showing the user authentication process with login forms and error handling screens.',
    required: false
  })
  @Column('text', { nullable: true })
  contentDescription: string;

  @ApiProperty({
    description: 'Metadata about content analysis including model used and analysis type',
    example: '{"model": "gemini-2.0-flash", "analysisType": "multimodal", "hasImages": true, "imageCount": 3}',
    required: false
  })
  @Column('json', { nullable: true })
  contentAnalysisMetadata: any;

  @ApiProperty({
    description: 'Status of content analysis processing',
    example: 'completed',
    enum: ['pending', 'processing', 'completed', 'failed'],
    required: false
  })
  @Column({ nullable: true, default: 'pending' })
  contentAnalysisStatus: string;

  @ApiProperty({
    description: 'When the file was uploaded',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the file was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}