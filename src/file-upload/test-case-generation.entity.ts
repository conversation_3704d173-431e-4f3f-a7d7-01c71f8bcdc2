import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { FileUpload } from './file-upload.entity';
import { PublishUrl } from '../publish-url/publish-url.entity';

@Entity('test_case_generation')
export class TestCaseGeneration {
  @ApiProperty({
    description: 'The unique identifier of the test case generation',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Test case name',
    example: 'Verify Filter Unread Feature Accessibility'
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Precondition for the test case',
    example: 'User is logged in'
  })
  @Column({ nullable: true })
  precondition: string;

  @ApiProperty({
    description: 'Test case steps',
    example: ['Login as a user', 'Navigate to inbox menu']
  })
  @Column('text', { array: true })
  steps: string[];

  @ApiProperty({
    description: 'Expected result',
    example: 'The Unread filter option should be visible and accessible'
  })
  @Column('text')
  expectation: string;

  @ApiProperty({
    description: 'Test case type',
    example: 'Functional'
  })
  @Column()
  testType: string;

  @ApiProperty({
    description: 'Priority level',
    example: 'Critical'
  })
  @Column()
  priority: string;

  @ApiProperty({
    description: 'Platform type',
    example: 'web'
  })
  @Column()
  platform: string;

  @ApiProperty({
    description: 'Type of test case',
    example: 'manual'
  })
  @Column()
  testCaseType: string;

  @ApiProperty({
    description: 'Automation by agentq',
    example: true
  })
  @Column({ default: false })
  automationByAgentq: boolean;

  @ApiProperty({
    description: 'The file upload this test case belongs to',
    type: () => FileUpload
  })
  @ManyToOne(() => FileUpload, { onDelete: 'CASCADE' })
  fileUpload: FileUpload;

  @ApiProperty({
    description: 'The ID of the file upload',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  fileUploadId: string;

  @ApiProperty({
    description: 'The publish URL this test case belongs to',
    type: () => PublishUrl
  })
  @ManyToOne(() => PublishUrl, { onDelete: 'CASCADE', nullable: true })
  publishUrl: PublishUrl;

  @ApiProperty({
    description: 'The ID of the publish URL',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ nullable: true })
  publishUrlId: string;

  @ApiProperty({
    description: 'When the test case was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;
}