import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { FileUploadController } from './file-upload.controller';
import { FileUploadService } from './file-upload.service';
import { FileUpload } from './file-upload.entity';
import { Embedding } from './embedding.entity';
import { TokenUsage } from './token-usage.entity';
import { TestCaseGeneration } from './test-case-generation.entity';
import { GoogleStorageService } from './google.storage.service';
import { LLMService } from './openai.service';
import { FileProcessorService } from './file-processor.service';
import { ContentAnalysisService } from './content-analysis.service';
import { EmbeddingProcessor } from './embedding.processor';
import { ApiDocumentationDetectorService } from '../publish-url/api-documentation-detector.service';
import { ApiDocumentationParserService } from '../publish-url/api-documentation-parser.service';
import { McpModule } from '../mcp/mcp.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FileUpload, Embedding, TokenUsage, TestCaseGeneration]),
    BullModule.registerQueue({
      name: 'embeddings',
    }),
    McpModule,
  ],
  controllers: [FileUploadController],
  providers: [
    FileUploadService,
    GoogleStorageService,
    LLMService,
    FileProcessorService,
    ContentAnalysisService,
    EmbeddingProcessor,
    ApiDocumentationDetectorService,
    ApiDocumentationParserService,
  ],
})
export class FileUploadModule {}