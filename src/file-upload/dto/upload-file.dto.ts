import { ApiProperty } from '@nestjs/swagger';

export class UploadFileDto {
  @ApiProperty({
    type: 'array',
    items: {
      type: 'string',
      format: 'binary'
    },
    description: 'Files to upload (Supported formats: .doc, .docx, .md, .txt, .pdf, .html, .xls, .xlsx, .csv, .jpg, .jpeg, .png, .gif, .webp, .bmp, .tiff)'
  })
  'file[]': Express.Multer.File[];

  @ApiProperty({
    type: 'string',
    description: 'API Key for authentication'
  })
  apiKey: string;

  @ApiProperty({
    type: 'string',
    description: 'Name of the upload',
    required: false
  })
  name?: string;
}