import { ApiProperty } from '@nestjs/swagger';
import { TokenType } from '../token-usage.entity';

export class TokenUsageDetailDto {
  @ApiProperty({
    description: 'The type of token usage',
    enum: TokenType,
    example: TokenType.EMBEDDING_INPUT
  })
  tokenType: TokenType;

  @ApiProperty({
    description: 'Total tokens used for this type',
    example: 1500
  })
  totalTokens: number;

  @ApiProperty({
    description: 'Number of operations for this type',
    example: 10
  })
  operationCount: number;

  @ApiProperty({
    description: 'Average tokens per operation',
    example: 150
  })
  averageTokensPerOperation: number;
}

export class TokenUsageResponseDto {
  @ApiProperty({
    description: 'File ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  fileId: string;

  @ApiProperty({
    description: 'Total tokens used across all operations',
    example: 3000
  })
  totalTokensUsed: number;

  @ApiProperty({
    description: 'Detailed usage statistics by token type',
    type: [TokenUsageDetailDto]
  })
  usageByType: TokenUsageDetailDto[];

  @ApiProperty({
    description: 'When the first token was used',
    example: '2024-03-17T06:22:49.000Z'
  })
  firstUsageTime: Date;

  @ApiProperty({
    description: 'When the last token was used',
    example: '2024-03-17T06:22:51.000Z'
  })
  lastUsageTime: Date;
}