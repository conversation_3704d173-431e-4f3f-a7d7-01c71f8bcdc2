import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TokenUsage, TokenType } from './token-usage.entity';
import * as pdfjsLib from 'pdf-parse';

export interface ContentAnalysisResult {
  description: string;
  metadata: {
    model: string;
    analysisType: 'text-only' | 'multimodal';
    hasImages: boolean;
    imageCount?: number;
    confidence?: number;
    processingTime?: number;
  };
}

@Injectable()
export class ContentAnalysisService {
  private genAI: GoogleGenerativeAI;
  private readonly logger = new Logger(ContentAnalysisService.name);

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
  ) {
    this.genAI = new GoogleGenerativeAI(
      this.configService.get<string>('GEMINI_API_KEY')
    );
  }

  /**
   * Analyze file content using Gemini's multimodal capabilities
   */
  async analyzeFileContent(
    file: Express.Multer.File,
    fileId: string
  ): Promise<ContentAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // Check if file type supports multimodal analysis
      if (this.supportsMultimodalAnalysis(file.mimetype)) {
        return await this.performMultimodalAnalysis(file, fileId, startTime);
      } else {
        return await this.performTextOnlyAnalysis(file, fileId, startTime);
      }
    } catch (error) {
      this.logger.error(`Content analysis failed for ${file.originalname}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if file type supports multimodal analysis
   */
  private supportsMultimodalAnalysis(mimetype: string): boolean {
    const multimodalTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/bmp',
      'image/tiff'
    ];
    return multimodalTypes.includes(mimetype);
  }

  /**
   * Perform multimodal analysis using Gemini Vision
   */
  private async performMultimodalAnalysis(
    file: Express.Multer.File,
    fileId: string,
    startTime: number
  ): Promise<ContentAnalysisResult> {
    const model = this.genAI.getGenerativeModel({ 
      model: "gemini-2.0-flash",
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 2048,
      }
    });

    let fileData: any;
    let hasImages = false;
    let imageCount = 0;

    if (file.mimetype === 'application/pdf') {
      // For PDFs, we need to extract images and text separately
      const pdfAnalysis = await this.analyzePdfContent(file.buffer);
      hasImages = pdfAnalysis.hasImages;
      imageCount = pdfAnalysis.imageCount;
      
      if (hasImages) {
        // Use multimodal analysis for PDFs with images
        fileData = {
          inlineData: {
            data: file.buffer.toString('base64'),
            mimeType: file.mimetype
          }
        };
      } else {
        // Fall back to text-only analysis
        return await this.performTextOnlyAnalysis(file, fileId, startTime);
      }
    } else {
      // For image files
      hasImages = true;
      imageCount = 1;
      fileData = {
        inlineData: {
          data: file.buffer.toString('base64'),
          mimeType: file.mimetype
        }
      };
    }

    const prompt = this.createMultimodalAnalysisPrompt(file.originalname, file.mimetype);
    
    const result = await model.generateContent([prompt, fileData]);
    const response = await result.response;
    const description = response.text();

    // Record token usage - skip for now since we don't have embedding ID yet
    const processingTime = Date.now() - startTime;
    const estimatedTokens = Math.ceil((prompt.length + description.length) / 4);
    // TODO: Record token usage when embedding is created
    this.logger.log(`Content analysis used approximately ${estimatedTokens} tokens for file ${fileId}`);

    return {
      description,
      metadata: {
        model: 'gemini-2.0-flash',
        analysisType: 'multimodal',
        hasImages,
        imageCount,
        confidence: 0.9, // Gemini doesn't provide confidence scores, using default
        processingTime
      }
    };
  }

  /**
   * Perform text-only analysis for files without images
   */
  private async performTextOnlyAnalysis(
    file: Express.Multer.File,
    fileId: string,
    startTime: number
  ): Promise<ContentAnalysisResult> {
    const model = this.genAI.getGenerativeModel({ 
      model: "gemini-2.0-flash",
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 1024,
      }
    });

    // Extract text content
    let textContent: string;
    switch (file.mimetype) {
      case 'application/pdf':
        const pdfData = await pdfjsLib(file.buffer);
        textContent = pdfData.text;
        break;
      case 'text/plain':
      case 'text/markdown':
      case 'text/html':
      case 'text/csv':
        textContent = file.buffer.toString('utf-8');
        break;
      default:
        textContent = file.buffer.toString('utf-8');
    }

    const prompt = this.createTextOnlyAnalysisPrompt(file.originalname, textContent);
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const description = response.text();

    // Record token usage - skip for now since we don't have embedding ID yet
    const processingTime = Date.now() - startTime;
    const estimatedTokens = Math.ceil((prompt.length + description.length) / 4);
    // TODO: Record token usage when embedding is created
    this.logger.log(`Text analysis used approximately ${estimatedTokens} tokens for file ${fileId}`);

    return {
      description,
      metadata: {
        model: 'gemini-2.0-flash',
        analysisType: 'text-only',
        hasImages: false,
        imageCount: 0,
        confidence: 0.85,
        processingTime
      }
    };
  }

  /**
   * Analyze PDF to detect images
   */
  private async analyzePdfContent(buffer: Buffer): Promise<{ hasImages: boolean; imageCount: number }> {
    try {
      const pdfData = await pdfjsLib(buffer);
      // This is a simplified check - in a real implementation, you might want to use
      // a more sophisticated PDF parsing library to detect images
      const hasImages = pdfData.text.length < buffer.length * 0.1; // Heuristic: if text is much smaller than file size, likely has images
      return {
        hasImages,
        imageCount: hasImages ? 1 : 0 // Simplified - could be enhanced to count actual images
      };
    } catch (error) {
      this.logger.warn(`Failed to analyze PDF structure: ${error.message}`);
      return { hasImages: false, imageCount: 0 };
    }
  }

  /**
   * Create prompt for multimodal analysis
   */
  private createMultimodalAnalysisPrompt(filename: string, mimetype: string): string {
    return `
Analyze this file (${filename}, type: ${mimetype}) and provide a comprehensive description of its content.

Focus on:
1. **Visual Elements**: Describe any images, diagrams, charts, screenshots, or visual components
2. **Text Content**: Summarize the main textual information
3. **Structure**: Describe the layout, organization, and flow of information
4. **Context**: What appears to be the purpose or domain of this content
5. **Key Information**: Highlight important data, processes, or concepts shown

For test case generation purposes, pay special attention to:
- User interface elements (buttons, forms, menus, etc.)
- Workflow diagrams or process flows
- System architecture or technical diagrams
- Error states or validation scenarios
- Data structures or API specifications

Provide a detailed but concise description that would help in generating comprehensive test cases.
    `.trim();
  }

  /**
   * Create prompt for text-only analysis
   */
  private createTextOnlyAnalysisPrompt(filename: string, textContent: string): string {
    const truncatedContent = textContent.length > 8000 
      ? textContent.substring(0, 8000) + '...[truncated]'
      : textContent;

    return `
Analyze this text document (${filename}) and provide a comprehensive description of its content.

Content:
${truncatedContent}

Focus on:
1. **Main Topics**: What are the primary subjects or themes
2. **Structure**: How is the information organized
3. **Key Information**: Important data, processes, or concepts
4. **Context**: What domain or purpose does this serve
5. **Technical Details**: Any specifications, requirements, or technical information

For test case generation purposes, pay special attention to:
- Functional requirements or specifications
- User workflows or processes
- System behaviors or rules
- Data validation requirements
- Error handling scenarios
- API endpoints or technical interfaces

Provide a detailed but concise description that would help in generating comprehensive test cases.
    `.trim();
  }

  /**
   * Record token usage for billing and monitoring
   */
  private async recordTokenUsage(embeddingId: string, tokensUsed: number): Promise<void> {
    try {
      await this.tokenUsageRepository.save({
        embeddingId,
        tokenType: TokenType.EMBEDDING_INPUT,
        tokensUsed,
      });
    } catch (error) {
      this.logger.error(`Failed to record token usage: ${error.message}`);
    }
  }
}
