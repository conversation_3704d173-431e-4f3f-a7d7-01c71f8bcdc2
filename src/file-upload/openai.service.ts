import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { TokenUsage, TokenType } from './token-usage.entity';
import { ApiDocumentationMcpService } from '../mcp/api-documentation-mcp.service';

type LLMProvider = 'OPENAI' | 'GEMINI';

@Injectable()
export class LLMService {
  private provider: LLMProvider;
  private openai: OpenAI;
  private genAI: GoogleGenerativeAI;
  private readonly logger = new Logger(LLMService.name);

  constructor(
    private configService: ConfigService,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
    private mcpService: ApiDocumentationMcpService
  ) {
    this.provider = this.configService.get<LLMProvider>('LLM') || 'OPENAI';
    
    if (this.provider === 'OPENAI') {
      this.openai = new OpenAI({
        apiKey: this.configService.get<string>('OPENAI_API_KEY'),
      });
    } else {
      this.genAI = new GoogleGenerativeAI(
        this.configService.get<string>('GEMINI_API_KEY')
      );
    }
  }

  private async retryOperation<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: any;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        this.logger.warn(`Attempt ${attempt} failed: ${error.message}`);
        await new Promise(res => setTimeout(res, 1000 * Math.pow(2, attempt - 1))); // exponential backoff
      }
    }
    throw lastError;
  }  

  private async recordTokenUsage(
    embeddingId: string | string[],
    inputTokens: number,
    outputTokens: number = 0
  ) {
    if (Array.isArray(embeddingId)) {
      const tokensPerItem = Math.floor(inputTokens / embeddingId.length);
      await this.tokenUsageRepository.save(
        embeddingId.map(id => ({
          embeddingId: id,
          tokenType: TokenType.EMBEDDING_INPUT,
          tokensUsed: tokensPerItem
        }))
      );
    } else {
      await this.tokenUsageRepository.save([
        {
          embeddingId,
          tokenType: TokenType.COMPLETION_INPUT,
          tokensUsed: inputTokens
        },
        ...(outputTokens > 0 ? [{
          embeddingId,
          tokenType: TokenType.COMPLETION_OUTPUT,
          tokensUsed: outputTokens
        }] : [])
      ]);
    }
  }

  async createEmbedding(text: string, embeddingId: string): Promise<number[]> {
    if (this.provider === 'OPENAI') {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
        encoding_format: "float",
      });

      await this.recordTokenUsage(embeddingId, response.usage.total_tokens);
      return response.data[0].embedding;
    } else {
      const model = this.genAI.getGenerativeModel({ model: "gemini-embedding-001" });
      const result = await model.embedContent(text);
      
      // Estimate tokens (Gemini doesn't provide token counts)
      const estimatedTokens = Math.ceil(text.length / 4); // Rough approximation
      await this.recordTokenUsage(embeddingId, estimatedTokens);
      
      return result.embedding.values;
    }
  }

  async createEmbeddings(texts: string[], embeddingIds: string[]): Promise<number[][]> {
    if (this.provider === 'OPENAI') {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-3-small",
        input: texts,
        encoding_format: "float",
      });

      const tokensPerText = Math.floor(response.usage.total_tokens / texts.length);
      await this.recordTokenUsage(embeddingIds, tokensPerText);
      return response.data.map(item => item.embedding);
    } else {
      const model = this.genAI.getGenerativeModel({ model: "gemini-embedding-001" });
      const embeddings: number[][] = [];
      
      for (const text of texts) {
        const result = await model.embedContent(text);
        embeddings.push(result.embedding.values);
      }

      // Estimate tokens for all texts
      const totalTokens = texts.reduce((sum, text) => sum + Math.ceil(text.length / 4), 0);
      const tokensPerText = Math.floor(totalTokens / texts.length);
      await this.recordTokenUsage(embeddingIds, tokensPerText);
      
      return embeddings;
    }
  }

  /**
   * Safely parse JSON with improved error handling and recovery attempts
   */
  private safeJsonParse(jsonString: string): any {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      this.logger.warn(`JSON parsing error: ${error.message}. Attempting to fix the JSON...`);
      
      // Try to extract JSON if surrounded by markdown or other text
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (e) {
          // Still failed, continue to other recovery methods
        }
      }
      
      // Try to extract JSON with more aggressive pattern matching
      const jsonRegex = /(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/g;
      const matches = jsonString.match(jsonRegex);
      if (matches && matches.length > 0) {
        for (const potentialJson of matches) {
          try {
            return JSON.parse(potentialJson);
          } catch (e) {
            // Try the next match
          }
        }
      }
      
      // Fix common JSON syntax errors
      let fixedJson = jsonString;
      
      // Remove markdown formatting
      fixedJson = fixedJson.replace(/\*\*/g, '');
      fixedJson = fixedJson.replace(/```json|```/g, '');
      
      // Fix trailing commas in arrays and objects
      fixedJson = fixedJson.replace(/,(\s*[\]}])/g, '$1');
      
      // Fix missing commas between array elements
      fixedJson = fixedJson.replace(/}(\s*){/g, '},$1{');
      fixedJson = fixedJson.replace(/"(\s*){/g, '",$1{');
      
      // Fix unquoted property names
      fixedJson = fixedJson.replace(/(\s*)(\w+)(\s*):(\s*)/g, '$1"$2"$3:$4');
      
      try {
        return JSON.parse(fixedJson);
      } catch (e) {
        // Handle truncated JSON by trying to complete it
        if (e.message.includes('Unexpected end of JSON input')) {
          this.logger.warn('Attempting to fix truncated JSON...');

          // Try to close incomplete JSON structures
          let completedJson = fixedJson.trim();

          // Count open braces and brackets
          const openBraces = (completedJson.match(/\{/g) || []).length;
          const closeBraces = (completedJson.match(/\}/g) || []).length;
          const openBrackets = (completedJson.match(/\[/g) || []).length;
          const closeBrackets = (completedJson.match(/\]/g) || []).length;

          // Add missing closing braces and brackets
          for (let i = 0; i < openBrackets - closeBrackets; i++) {
            completedJson += ']';
          }
          for (let i = 0; i < openBraces - closeBraces; i++) {
            completedJson += '}';
          }

          try {
            return JSON.parse(completedJson);
          } catch (e2) {
            this.logger.warn('Failed to complete truncated JSON, trying to extract test cases...');
          }
        }

        this.logger.error(`Failed to fix JSON: ${e.message}`);
        this.logger.debug(`Original JSON: ${jsonString.substring(0, 500)}...`);

        // As a last resort, create a minimal valid structure
        return {
          testCases: [{
            name: "Error parsing LLM response",
            precondition: "JSON parsing failed",
            steps: ["See error logs for details"],
            expectation: "Valid JSON response",
            testType: "error",
            priority: "high",
            platform: "system",
            testCaseType: "manual",
            automationByAgentq: false
          }]
        };
      }
    }
  }

  async generateTestCases(content: string, embeddingId: string) {
    // Enhanced prompt with stronger emphasis on JSON format
    const prompt = `
      As a QA expert, analyze the following content and generate comprehensive test cases.
      For each test case, provide:
      1. A clear, descriptive name
      2. Preconditions to set up the test
      3. Detailed steps to execute the test
      4. Expected results
      5. Test type (functional, integration, performance, security, usability)
      6. Priority (critical, high, medium, low)
      7. Platform (only one platform per test case such as web, mobile, API)
      8. Test case type (manual)
      9. Automation by AgentQ (false)

      You MUST output VALID JSON exactly in this format:
      {
        "testCases": [
          {
            "name": "Test case name",
            "precondition": "Preconditions to set up the test",
            "steps": ["Step 1", "Step 2"],
            "expectation": "Expected result",
            "testType": "Type of test",
            "priority": "Priority level",
            "platform": "Platform type",
            "testCaseType": "Type of test case",
            "automationByAgentq": false
          }
        ]
      }

      Make sure to:
      - Use double quotes for strings and property names
      - Don't use trailing commas
      - Properly close all brackets and braces
      - Format arrays with square brackets
      - Keep the exact property names as shown above
      - DO NOT include any markdown formatting, explanations, or additional text
      - ONLY return the JSON object, nothing else
      
      Content to analyze:
      ${content}
    `;
    
    if (this.provider === 'OPENAI') {
      try {
        const response = await this.retryOperation(() =>
          this.openai.chat.completions.create({
          model: "gpt-4-turbo-preview",
          messages: [
            {
              role: "system",
              content: "You are a QA expert specialized in creating detailed test cases. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          response_format: { type: "json_object" },
          temperature: 0.5, // Lower temperature for more deterministic results
        })
        );

        await this.recordTokenUsage(
          embeddingId,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );

        // Use safe JSON parsing
        const result = this.safeJsonParse(response.choices[0].message.content);
        return result.testCases || [];
      } catch (error) {
        this.logger.error(`OpenAI error: ${error.message}`);
        return [];
      }
    } else {
      try {
        const model = this.genAI.getGenerativeModel({ 
          model: "gemini-2.0-flash",
          generationConfig: {
            responseMimeType: "application/json",
            temperature: 0.3, // Lower temperature for more reliable JSON
          }
        });

        // Enhanced system prompt for Gemini
        const systemPrompt = "You are a QA expert specialized in creating detailed test cases. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON.";
        
        const chat = model.startChat({
          history: [{
            role: "user",
            parts: [{ text: systemPrompt }]
          }]
        });

        const result = await this.retryOperation(() => chat.sendMessage(prompt));
        const response = await result.response;
        const text = response.text();
        
        // Estimate tokens (1 token ≈ 4 characters)
        const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
        const outputTokens = Math.ceil(text.length / 4);
        await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

        // Use safe JSON parsing
        const parsedResult = this.safeJsonParse(text);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`Gemini error: ${error.message}`);
        return [];
      }
    }
  }

  async generateApiTestCases(content: string, embeddingId: string, apiMetadata?: any) {
    // For Gemini, we can handle much larger content due to its large context window
    const isGemini = this.provider === 'GEMINI';
    const maxContentLength = isGemini ? 50000 : 8000; // Much larger limit for Gemini
    let processedContent = content;

    if (content.length > maxContentLength) {
      this.logger.warn(`API documentation content is large (${content.length} chars), ${isGemini ? 'using Gemini large context' : 'truncating'} to ${maxContentLength} chars`);

      if (!isGemini) {
        // For OpenAI, intelligently truncate by keeping the most important parts
        const lines = content.split('\n');
        const importantLines = lines.filter(line =>
          line.includes('API Endpoints') ||
          line.includes('GET ') || line.includes('POST ') || line.includes('PUT ') ||
          line.includes('PATCH ') || line.includes('DELETE ') ||
          line.includes('Summary:') || line.includes('Description:') ||
          line.includes('Parameters:') || line.includes('Tags:')
        );

        // If we have important lines, use them; otherwise, truncate normally
        if (importantLines.length > 0 && importantLines.join('\n').length <= maxContentLength) {
          processedContent = importantLines.join('\n');
        } else {
          processedContent = content.substring(0, maxContentLength) + '\n... (content truncated)';
        }
      } else {
        // For Gemini, we can use more content but still limit to prevent issues
        processedContent = content.substring(0, maxContentLength);
      }
    }

    // Enhanced prompt specifically for API documentation
    const prompt = `
      As a QA expert specializing in API testing, analyze the following API documentation and generate comprehensive test cases.
      Focus on API-specific testing scenarios including:

      1. Endpoint validation tests
      2. HTTP method tests (GET, POST, PUT, DELETE, etc.)
      3. Parameter validation tests (required/optional, data types, formats)
      4. Authentication and authorization tests
      5. Request/response format tests
      6. Error handling tests (4xx, 5xx status codes)
      7. Performance and load tests
      8. Security tests (injection, authentication bypass)
      9. Integration tests between endpoints
      10. Data validation tests

      For each test case, provide:
      1. A clear, descriptive name that includes the endpoint and scenario
      2. Preconditions including API setup, authentication, test data
      3. Detailed steps including HTTP method, endpoint, headers, body
      4. Expected results including status codes, response format, data validation
      5. Test type (functional, integration, performance, security, usability)
      6. Priority (critical, high, medium, low)
      7. Platform (API)
      8. Test case type (manual)
      9. Automation by AgentQ (false)

      IMPORTANT: ${isGemini ? 'Generate comprehensive test cases covering ALL endpoints found in the API documentation. Aim for 3-5 test cases per endpoint to ensure thorough coverage.' : 'Generate a maximum of 15 test cases to ensure response quality.'}
      Focus on ${isGemini ? 'comprehensive coverage of all endpoints with diverse test scenarios including positive, negative, edge cases, and security tests' : 'the most critical and diverse test scenarios'}.

      You MUST output VALID JSON exactly in this format:
      {
        "testCases": [
          {
            "name": "API test case name with endpoint",
            "precondition": "API setup, authentication, test data requirements",
            "steps": ["Action"],
            "expectation": "Expected outcome with status codes and response validation",
            "testType": "Type of test",
            "priority": "Priority level",
            "platform": "API",
            "testCaseType": "manual",
            "automationByAgentq": false
          }
        ]
      }

      Make sure to:
      - Generate test cases for the most important API endpoints found
      - Include both positive and negative test scenarios
      - Test different HTTP methods for each endpoint
      - Validate request parameters and response data
      - Test error conditions and edge cases
      - Use double quotes for strings and property names
      - Don't use trailing commas
      - Properly close all brackets and braces
      - Format arrays with square brackets
      - Keep the exact property names as shown above
      - DO NOT include any markdown formatting, explanations, or additional text
      - ONLY return the JSON object, nothing else

      API Documentation to analyze:
      ${processedContent}

      ${apiMetadata ? `Additional API Metadata: ${JSON.stringify(apiMetadata)}` : ''}
    `;

    if (this.provider === 'OPENAI') {
      try {
        const response = await this.retryOperation(() =>
          this.openai.chat.completions.create({
            model: 'gpt-4o-mini',
            messages: [
              {
                role: 'system',
                content: 'You are a QA expert specialized in API testing. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.3,
            max_tokens: 4000,
          })
        );

        const content = response.choices[0]?.message?.content;
        if (!content) {
          throw new Error('No content in OpenAI response');
        }

        // Record token usage
        await this.recordTokenUsage(
          embeddingId,
          response.usage?.prompt_tokens || 0,
          response.usage?.completion_tokens || 0
        );

        // Use safe JSON parsing
        const parsedResult = this.safeJsonParse(content);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`OpenAI API error in generateApiTestCases: ${error.message}`);
        return [];
      }
    } else {
      try {
        const model = this.genAI.getGenerativeModel({
          model: "gemini-2.0-flash",
          generationConfig: {
            responseMimeType: "application/json",
            temperature: 0.3,
            maxOutputTokens: 8192, // Increased output limit for comprehensive test cases
          }
        });

        const systemPrompt = "You are a QA expert specialized in API testing. You must provide output in valid JSON format with the exact structure requested. No markdown, no explanations, ONLY valid JSON.";

        const chat = model.startChat({
          history: [{
            role: "user",
            parts: [{ text: systemPrompt }]
          }]
        });

        const result = await this.retryOperation(() => chat.sendMessage(prompt));
        const response = await result.response;
        const text = response.text();

        // Estimate tokens
        const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
        const outputTokens = Math.ceil(text.length / 4);
        await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

        // Use safe JSON parsing
        const parsedResult = this.safeJsonParse(text);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`Gemini error in generateApiTestCases: ${error.message}`);
        return [];
      }
    }
  }

  async generateComprehensiveApiTestCases(content: string, embeddingId: string, apiMetadata?: any) {
    // This method is specifically designed for large API documentation
    // It uses chunking to handle very large APIs and prevent JSON truncation

    if (this.provider !== 'GEMINI') {
      // Fallback to regular API test case generation for OpenAI
      return this.generateApiTestCases(content, embeddingId, apiMetadata);
    }

    // For very large APIs, chunk the content to prevent JSON truncation
    const endpointCount = apiMetadata?.endpoints || 0;
    if (endpointCount > 50 || content.length > 40000) {
      this.logger.log(`Very large API detected (${endpointCount} endpoints, ${content.length} chars), using chunked generation`);
      return this.generateApiTestCasesInChunks(content, embeddingId, apiMetadata);
    }

    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
        generationConfig: {
          responseMimeType: "application/json",
          temperature: 0.2, // Lower temperature for more consistent output
          maxOutputTokens: 4096, // Reduced to prevent truncation
        }
      });

      // Enhanced prompt for comprehensive API testing
      const prompt = `
        As a senior QA automation expert specializing in API testing, analyze the following comprehensive API documentation and generate an extensive test suite.

        Your goal is to create a thorough test suite that covers:

        1. **Functional Testing** (for each endpoint):
           - Happy path scenarios with valid data
           - Boundary value testing
           - Data validation testing
           - Response format validation

        2. **Negative Testing**:
           - Invalid parameters
           - Missing required fields
           - Invalid data types
           - Malformed requests

        3. **Security Testing**:
           - Authentication bypass attempts
           - Authorization testing
           - Input injection testing (SQL, XSS, etc.)
           - Rate limiting validation

        4. **Performance Testing**:
           - Load testing scenarios
           - Stress testing with large payloads
           - Concurrent request testing

        5. **Integration Testing**:
           - Cross-endpoint workflows
           - Data consistency across endpoints
           - Transaction testing

        6. **Error Handling**:
           - HTTP status code validation
           - Error message validation
           - Exception handling testing

        **IMPORTANT INSTRUCTIONS:**
        - Generate test cases for EVERY endpoint found in the API documentation
        - Create 3-5 test cases per endpoint covering different scenarios
        - Include both positive and negative test cases
        - Focus on real-world usage scenarios
        - Ensure comprehensive coverage of all HTTP methods
        - Include edge cases and boundary conditions

        For each test case, provide:
        1. **name**: Clear, descriptive name including endpoint and scenario
        2. **precondition**: Setup requirements, authentication, test data
        3. **steps**: Detailed execution steps with HTTP method, endpoint, headers, body
        4. **expectation**: Expected status code, response format, data validation
        5. **testType**: functional, security, performance, integration, negative
        6. **priority**: critical, high, medium, low
        7. **platform**: "API"
        8. **testCaseType**: "manual"
        9. **automationByAgentq**: false

        Output ONLY valid JSON in this exact format:
        {
          "testCases": [
            {
              "name": "Test case name with endpoint and scenario",
              "precondition": "Setup requirements and prerequisites",
              "steps": ["Action"],
              "expectation": "Expected outcome with status codes and response validation",
              "testType": "functional|security|performance|integration|negative",
              "priority": "critical|high|medium|low",
              "platform": "API",
              "testCaseType": "manual",
              "automationByAgentq": false
            }
          ]
        }

        API Documentation to analyze:
        ${content}

        ${apiMetadata ? `API Metadata: ${JSON.stringify(apiMetadata)}` : ''}

        Generate comprehensive test cases covering all endpoints and scenarios.
      `;

      const systemPrompt = "You are a senior QA automation expert. Generate comprehensive API test cases in valid JSON format. Focus on thorough coverage of all endpoints with multiple test scenarios per endpoint.";

      const chat = model.startChat({
        history: [{
          role: "user",
          parts: [{ text: systemPrompt }]
        }]
      });

      const result = await this.retryOperation(() => chat.sendMessage(prompt));
      const response = await result.response;
      const text = response.text();

      // Estimate tokens
      const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
      const outputTokens = Math.ceil(text.length / 4);
      await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

      // Use safe JSON parsing
      const parsedResult = this.safeJsonParse(text);

      this.logger.log(`Generated ${parsedResult.testCases?.length || 0} comprehensive API test cases`);
      return parsedResult.testCases || [];
    } catch (error) {
      this.logger.error(`Gemini error in generateComprehensiveApiTestCases: ${error.message}`);
      // Fallback to regular API test case generation
      return this.generateApiTestCases(content, embeddingId, apiMetadata);
    }
  }

  async generateApiTestCasesInChunks(content: string, embeddingId: string, apiMetadata?: any) {
    // Split large API documentation into manageable chunks
    this.logger.log('Generating API test cases in chunks to handle large documentation');

    try {
      // Split content by endpoints
      const lines = content.split('\n');
      const chunks: string[] = [];
      let currentChunk = '';
      let currentChunkSize = 0;
      const maxChunkSize = 15000; // Smaller chunks to ensure reliable processing

      // Add API header information to each chunk
      const headerLines = lines.slice(0, 10).join('\n'); // First 10 lines usually contain API info

      for (const line of lines) {
        // Start a new chunk when we hit an endpoint or reach size limit
        if ((line.includes('GET ') || line.includes('POST ') || line.includes('PUT ') ||
             line.includes('PATCH ') || line.includes('DELETE ')) && currentChunkSize > 5000) {
          if (currentChunk.trim()) {
            chunks.push(headerLines + '\n\n' + currentChunk);
          }
          currentChunk = line + '\n';
          currentChunkSize = line.length;
        } else {
          currentChunk += line + '\n';
          currentChunkSize += line.length;
        }

        // Force chunk split if too large
        if (currentChunkSize > maxChunkSize) {
          if (currentChunk.trim()) {
            chunks.push(headerLines + '\n\n' + currentChunk);
          }
          currentChunk = '';
          currentChunkSize = 0;
        }
      }

      // Add remaining content
      if (currentChunk.trim()) {
        chunks.push(headerLines + '\n\n' + currentChunk);
      }

      this.logger.log(`Split API documentation into ${chunks.length} chunks`);

      // Generate test cases for each chunk
      const allTestCases: any[] = [];

      for (let i = 0; i < chunks.length; i++) {
        this.logger.log(`Processing chunk ${i + 1}/${chunks.length}`);

        try {
          const chunkTestCases = await this.generateApiTestCasesForChunk(
            chunks[i],
            embeddingId,
            apiMetadata,
            i + 1,
            chunks.length
          );

          if (chunkTestCases && chunkTestCases.length > 0) {
            allTestCases.push(...chunkTestCases);
            this.logger.log(`Generated ${chunkTestCases.length} test cases for chunk ${i + 1}`);
          }

          // Small delay between chunks to avoid rate limiting
          if (i < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (error) {
          this.logger.error(`Error processing chunk ${i + 1}: ${error.message}`);
          // Continue with other chunks
        }
      }

      this.logger.log(`Generated total of ${allTestCases.length} test cases from ${chunks.length} chunks`);
      return allTestCases;

    } catch (error) {
      this.logger.error(`Error in chunked API test case generation: ${error.message}`);
      // Fallback to regular generation
      return this.generateApiTestCases(content, embeddingId, apiMetadata);
    }
  }

  async generateApiTestCasesForChunk(content: string, embeddingId: string, apiMetadata?: any, chunkNumber?: number, totalChunks?: number) {
    // Generate test cases for a single chunk of API documentation
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
        generationConfig: {
          responseMimeType: "application/json",
          temperature: 0.3,
          maxOutputTokens: 3072, // Conservative limit to prevent truncation
        }
      });

      const prompt = `
        As a QA expert, analyze this section of API documentation and generate comprehensive test cases.

        ${chunkNumber ? `This is chunk ${chunkNumber} of ${totalChunks} from a larger API documentation.` : ''}

        Focus on creating thorough test cases for the endpoints in this section:

        1. **Functional Testing**: Happy path scenarios with valid data
        2. **Negative Testing**: Invalid parameters, missing fields, wrong data types
        3. **Security Testing**: Authentication, authorization, input validation
        4. **Error Handling**: HTTP status codes, error responses

        Generate 2-4 test cases per endpoint found in this section.

        Output ONLY valid JSON in this format:
        {
          "testCases": [
            {
              "name": "Test case name with endpoint",
              "precondition": "Setup requirements",
              "steps": ["Step 1", "Step 2", "Step 3"],
              "expectation": "Expected outcome",
              "testType": "functional|security|negative|integration",
              "priority": "critical|high|medium|low",
              "platform": "API",
              "testCaseType": "manual",
              "automationByAgentq": false
            }
          ]
        }

        API Documentation Section:
        ${content}
      `;

      const systemPrompt = "You are a QA expert. Generate comprehensive API test cases in valid JSON format. Focus on quality over quantity.";

      const chat = model.startChat({
        history: [{
          role: "user",
          parts: [{ text: systemPrompt }]
        }]
      });

      const result = await this.retryOperation(() => chat.sendMessage(prompt));
      const response = await result.response;
      const text = response.text();

      // Estimate tokens for this chunk
      const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
      const outputTokens = Math.ceil(text.length / 4);
      await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

      // Use safe JSON parsing
      const parsedResult = this.safeJsonParse(text);
      return parsedResult.testCases || [];

    } catch (error) {
      this.logger.error(`Error generating test cases for chunk: ${error.message}`);
      return [];
    }
  }

  async generateMcpApiTestCases(apiId: string, embeddingId: string): Promise<any[]> {
    // MCP-based API test generation - much more intelligent and comprehensive
    this.logger.log(`Generating MCP-based API test cases for ${apiId}`);

    if (this.provider !== 'GEMINI') {
      this.logger.warn('MCP-based test generation works best with Gemini, falling back to regular generation');
      // Could fallback to regular generation, but for now we'll proceed with Gemini
    }

    try {
      // Get comprehensive API structure using MCP
      const apiStructure = await this.mcpService.getApiStructureForTesting(apiId);
      const endpointsByPriority = await this.mcpService.getEndpointsByPriority(apiId);

      this.logger.log(`API Structure: ${apiStructure.endpoints.length} endpoints, ${Object.keys(apiStructure.schemas).length} schemas`);

      // Generate test cases in batches by priority
      const allTestCases: any[] = [];

      // Process critical endpoints first
      if (endpointsByPriority.critical.length > 0) {
        this.logger.log(`Processing ${endpointsByPriority.critical.length} critical endpoints`);
        const criticalTests = await this.generateTestCasesForEndpoints(
          apiId,
          endpointsByPriority.critical,
          apiStructure,
          'critical',
          embeddingId
        );
        allTestCases.push(...criticalTests);
      }

      // Process high priority endpoints
      if (endpointsByPriority.high.length > 0) {
        this.logger.log(`Processing ${endpointsByPriority.high.length} high priority endpoints`);
        const highTests = await this.generateTestCasesForEndpoints(
          apiId,
          endpointsByPriority.high,
          apiStructure,
          'high',
          embeddingId
        );
        allTestCases.push(...highTests);
      }

      // Process medium priority endpoints (limit to prevent too many tests)
      const mediumEndpoints = endpointsByPriority.medium.slice(0, 10); // Limit to 10
      if (mediumEndpoints.length > 0) {
        this.logger.log(`Processing ${mediumEndpoints.length} medium priority endpoints`);
        const mediumTests = await this.generateTestCasesForEndpoints(
          apiId,
          mediumEndpoints,
          apiStructure,
          'medium',
          embeddingId
        );
        allTestCases.push(...mediumTests);
      }

      // Process some low priority endpoints (limit to prevent too many tests)
      const lowEndpoints = endpointsByPriority.low.slice(0, 5); // Limit to 5
      if (lowEndpoints.length > 0) {
        this.logger.log(`Processing ${lowEndpoints.length} low priority endpoints`);
        const lowTests = await this.generateTestCasesForEndpoints(
          apiId,
          lowEndpoints,
          apiStructure,
          'low',
          embeddingId
        );
        allTestCases.push(...lowTests);
      }

      this.logger.log(`Generated total of ${allTestCases.length} MCP-based API test cases`);
      return allTestCases;

    } catch (error) {
      this.logger.error(`Error in MCP-based API test generation: ${error.message}`);
      throw error;
    }
  }

  private async generateTestCasesForEndpoints(
    apiId: string,
    endpoints: any[],
    apiStructure: any,
    priority: string,
    embeddingId: string
  ): Promise<any[]> {
    try {
      // Get detailed information for these endpoints
      const endpointDetails = await this.mcpService.getEndpointsWithDetails(
        apiId,
        endpoints.map(ep => ({ path: ep.path, method: ep.method }))
      );

      // Create intelligent prompt using MCP data
      const prompt = this.createMcpBasedPrompt(endpointDetails, apiStructure, priority);

      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
        generationConfig: {
          responseMimeType: "application/json",
          temperature: 0.3,
          maxOutputTokens: 4096,
        }
      });

      const systemPrompt = `You are a senior QA automation expert with deep API testing expertise. Generate comprehensive, intelligent test cases using the provided structured API information.`;

      const chat = model.startChat({
        history: [{
          role: "user",
          parts: [{ text: systemPrompt }]
        }]
      });

      const result = await this.retryOperation(() => chat.sendMessage(prompt));
      const response = await result.response;
      const text = response.text();

      // Estimate tokens
      const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
      const outputTokens = Math.ceil(text.length / 4);
      await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

      // Parse results
      const parsedResult = this.safeJsonParse(text);
      const testCases = parsedResult.testCases || [];

      this.logger.log(`Generated ${testCases.length} test cases for ${endpoints.length} ${priority} priority endpoints`);
      return testCases;

    } catch (error) {
      this.logger.error(`Error generating test cases for ${priority} endpoints:`, error);
      return [];
    }
  }

  private createMcpBasedPrompt(endpointDetails: any[], apiStructure: any, priority: string): string {
    return `
      Generate comprehensive API test cases for the following ${priority} priority endpoints using the provided structured API information.

      **API Information:**
      - Title: ${apiStructure.apiInfo.title}
      - Base URL: ${apiStructure.apiInfo.baseUrl}
      - Version: ${apiStructure.apiInfo.version}
      - Total Endpoints: ${apiStructure.apiInfo.endpointCount}

      **Authentication:**
      ${JSON.stringify(apiStructure.authentication, null, 2)}

      **Available Schemas:**
      ${JSON.stringify(apiStructure.schemas, null, 2)}

      **Endpoints to Test:**
      ${JSON.stringify(endpointDetails, null, 2)}

      **Test Generation Requirements:**

      For each endpoint, generate 2-4 test cases covering:
      1. **Happy Path**: Valid request with expected response
      2. **Validation Testing**: Invalid parameters, missing required fields
      3. **Security Testing**: Authentication/authorization scenarios
      4. **Error Handling**: Expected error responses (4xx, 5xx)

      **Output Format:**
      Generate ONLY valid JSON in this exact format:
      {
        "testCases": [
          {
            "name": "Descriptive test name with endpoint and scenario",
            "precondition": "Setup requirements including authentication and test data",
            "steps": [
              "Step 1: Setup authentication/headers",
              "Step 2: Prepare request data",
              "Step 3: Send HTTP request to {method} {endpoint}",
              "Step 4: Validate response"
            ],
            "expectation": "Expected status code, response structure, and data validation",
            "testType": "functional|security|negative|integration",
            "priority": "${priority}",
            "platform": "API",
            "testCaseType": "manual",
            "automationByAgentq": false
          }
        ]
      }

      **Important:**
      - Use the actual endpoint paths and methods from the provided data
      - Reference specific schema names when validating request/response data
      - Include authentication requirements from the API documentation
      - Generate realistic test data based on parameter types and constraints
      - Focus on practical, executable test scenarios
      - Ensure all JSON is properly formatted with no trailing commas
    `;
  }

  async generateTestCasesWithGemini(content: string, embeddingId: string) {
    if (this.provider !== 'GEMINI') {
      return this.generateTestCases(content, embeddingId);
    }

    try {
      // Using a different approach with Gemini
      const model = this.genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
      
      // Break the prompt into multiple steps
      const initialPrompt = `As a QA expert, analyze this content and prepare to create test cases: ${content}`;
      
      const structurePrompt = `
        Now, create JSON-formatted test cases with the following structure:
        {
          "testCases": [
            {
              "name": "Test case name",
              "precondition": "Preconditions to set up the test",
              "steps": ["Step 1", "Step 2"],
              "expectation": "Expected result",
              "testType": "Type of test",
              "priority": "Priority level",
              "platform": "Platform type",
              "testCaseType": "manual",
              "automationByAgentq": false
            }
          ]
        }
        
        Make sure to:
        - Use double quotes for all strings and property names
        - Don't use trailing commas
        - Format arrays properly with square brackets
        - Include 2-4 test cases, each with 2-4 steps
        - Return ONLY the JSON, nothing else
      `;
      
      // Create a multi-turn chat
      const chat = model.startChat();
      
      // Step 1: Analyze the content
      await chat.sendMessage(initialPrompt);
      
      // Step 2: Request JSON format
      const result = await chat.sendMessage(structurePrompt);
      const response = await result.response;
      const text = response.text();
      
      // Estimate tokens
      const inputTokens = Math.ceil((initialPrompt.length + structurePrompt.length) / 4);
      const outputTokens = Math.ceil(text.length / 4);
      await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);
      
      // Try to extract just the JSON part
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[0] : text;
      
      // Use safe JSON parsing
      const parsedResult = this.safeJsonParse(jsonText);
      return parsedResult.testCases || [];
    } catch (error) {
      this.logger.error(`Alternative Gemini approach error: ${error.message}`);
      return [];
    }
  }

  async generateMoreTestCases(content: string, embeddingId: string, existingScenarios: any[]) {
    const existingScenariosText = JSON.stringify(existingScenarios, null, 2);
    
    const prompt = `
      As a QA expert, analyze the following content and generate additional comprehensive test cases.
      IMPORTANT: Generate completely new test scenarios that are different from the existing ones.

      Existing test scenarios:
      ${existingScenariosText}

      For each new test case, provide:
      1. A clear, descriptive name (must be unique)
      2. Preconditions to set up the test
      3. Detailed steps to execute the test
      4. Expected results
      5. Test type (functional, integration, performance, security, usability)
      6. Priority (critical, high, medium, low)
      7. Platform (only one platform per test case such as web, mobile, API)
      8. Test case type (manual)
      9. Automation by AgentQ (false)

      You MUST output VALID JSON exactly in this format:
      {
        "testCases": [
          {
            "name": "Test case name",
            "precondition": "Preconditions to set up the test",
            "steps": ["Step 1", "Step 2"],
            "expectation": "Expected result",
            "testType": "Type of test",
            "priority": "Priority level",
            "platform": "Platform type",
            "testCaseType": "Type of test case",
            "automationByAgentq": false
          }
        ]
      }

      Content to analyze:
      ${content}
    `;

    if (this.provider === 'OPENAI') {
      try {
        const response = await this.retryOperation(() =>
          this.openai.chat.completions.create({
          model: "gpt-4-turbo-preview",
          messages: [
            {
              role: "system",
              content: "You are a QA expert specialized in creating detailed test cases. Generate new, unique test cases that don't overlap with existing ones. Output in valid JSON format only."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          response_format: { type: "json_object" },
          temperature: 0.7, // Slightly higher temperature for more creative results
        })
        );

        await this.recordTokenUsage(
          embeddingId,
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );

        const result = this.safeJsonParse(response.choices[0].message.content);
        return result.testCases || [];
      } catch (error) {
        this.logger.error(`OpenAI error: ${error.message}`);
        return [];
      }
    } else {
      try {
        const model = this.genAI.getGenerativeModel({ 
          model: "gemini-2.0-flash",
          generationConfig: {
            responseMimeType: "application/json",
            temperature: 0.3,
          }
        });

        const systemPrompt = "You are a QA expert specialized in creating detailed test cases. Generate new, unique test cases that don't overlap with existing ones. Output in valid JSON format only.";
        
        const chat = model.startChat({
          history: [{
            role: "user",
            parts: [{ text: systemPrompt }]
          }]
        });

        const result = await this.retryOperation(() => chat.sendMessage(prompt));
        const response = await result.response;
        const text = response.text();
        
        const inputTokens = Math.ceil((systemPrompt.length + prompt.length) / 4);
        const outputTokens = Math.ceil(text.length / 4);
        await this.recordTokenUsage(embeddingId, inputTokens, outputTokens);

        const parsedResult = this.safeJsonParse(text);
        return parsedResult.testCases || [];
      } catch (error) {
        this.logger.error(`Gemini error: ${error.message}`);
        return [];
      }
    }
  }
}
