import { <PERSON>ti<PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Embedding } from './embedding.entity';

export enum TokenType {
  EMBEDDING_INPUT = 'embedding_input',
  COMPLETION_INPUT = 'completion_input',
  COMPLETION_OUTPUT = 'completion_output'
}

@Entity('token_usage')
export class TokenUsage {
  @ApiProperty({
    description: 'The unique identifier of the token usage record',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The embedding this token usage is associated with',
    type: () => Embedding
  })
  @ManyToOne(() => Embedding, { onDelete: 'CASCADE' })
  embedding: Embedding;

  @ApiProperty({
    description: 'The ID of the embedding',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  embeddingId: string;

  @ApiProperty({
    description: 'The type of token usage',
    enum: TokenType,
    example: TokenType.EMBEDDING_INPUT
  })
  @Column({
    type: 'enum',
    enum: TokenType
  })
  tokenType: TokenType;

  @ApiProperty({
    description: 'Number of tokens used',
    example: 150
  })
  @Column()
  tokensUsed: number;

  @ApiProperty({
    description: 'When the token usage was recorded',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  timestamp: Date;
}