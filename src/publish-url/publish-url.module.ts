import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { PublishUrlController } from './publish-url.controller';
import { PublishUrlService } from './publish-url.service';
import { PublishUrl } from './publish-url.entity';
import { Embedding } from '../file-upload/embedding.entity';
import { TokenUsage } from '../file-upload/token-usage.entity';
import { TestCaseGeneration } from '../file-upload/test-case-generation.entity';
import { LLMService } from '../file-upload/openai.service';
import { ApiDocumentationDetectorService } from './api-documentation-detector.service';
import { ApiDocumentationParserService } from './api-documentation-parser.service';
import { McpModule } from '../mcp/mcp.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PublishUrl, Embedding, TokenUsage, TestCaseGeneration]),
    BullModule.registerQueue({
      name: 'embeddings',
    }),
    McpModule,
  ],
  controllers: [PublishUrlController],
  providers: [
    PublishUrlService,
    LLMService,
    ApiDocumentationDetectorService,
    ApiDocumentationParserService
  ],
})
export class PublishUrlModule {}