import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { Repository, In } from 'typeorm';
import { PublishUrl, UrlType, UrlContentType } from './publish-url.entity';
import { Embedding } from '../file-upload/embedding.entity';
import { CreatePublishUrlDto } from './dto/create-publish-url.dto';
import { CreatePublishPrivateUrlDto } from './dto/create-publish-private-url.dto';
import { TokenUsage, TokenType } from '../file-upload/token-usage.entity';
import { TokenUsageResponseDto } from '../file-upload/dto/token-usage-response.dto';
import { TestCaseGeneration } from '../file-upload/test-case-generation.entity';
import { LLMService } from '../file-upload/openai.service';
import { ApiDocumentationDetectorService } from './api-documentation-detector.service';
import { ApiDocumentationParserService } from './api-documentation-parser.service';
import { ApiDocumentationMcpService } from '../mcp/api-documentation-mcp.service';

@Injectable()
export class PublishUrlService {
  private readonly logger = new Logger(PublishUrlService.name);

  constructor(
    @InjectRepository(PublishUrl)
    private publishUrlRepository: Repository<PublishUrl>,
    @InjectRepository(Embedding)
    private embeddingRepository: Repository<Embedding>,
    @InjectRepository(TokenUsage)
    private tokenUsageRepository: Repository<TokenUsage>,
    @InjectRepository(TestCaseGeneration)
    private testCaseGenerationRepository: Repository<TestCaseGeneration>,
    @InjectQueue('embeddings')
    private embeddingsQueue: Queue,
    private openAIService: LLMService,
    private apiDocumentationDetector: ApiDocumentationDetectorService,
    private apiDocumentationParser: ApiDocumentationParserService,
    private mcpService: ApiDocumentationMcpService,
  ) {}

  async create(createPublishUrlDto: CreatePublishUrlDto): Promise<PublishUrl> {
    // Detect if URL is API documentation
    const apiDetectionInfo = await this.apiDocumentationDetector.detectApiDocumentation(createPublishUrlDto.url);

    // Set content type and metadata based on detection
    const publishUrlData = {
      ...createPublishUrlDto,
      content_type: apiDetectionInfo.isApiDocumentation ? UrlContentType.API_DOCUMENTATION : UrlContentType.WEBSITE,
      api_metadata: apiDetectionInfo.isApiDocumentation ? {
        type: apiDetectionInfo.type,
        version: apiDetectionInfo.version,
        title: apiDetectionInfo.title,
        baseUrl: apiDetectionInfo.baseUrl,
        endpoints: apiDetectionInfo.endpoints,
        detectionMetadata: apiDetectionInfo.metadata
      } : null
    };

    const publishUrl = this.publishUrlRepository.create(publishUrlData);
    const savedUrl = await this.publishUrlRepository.save(publishUrl);

    this.logger.log(`Created publish URL: ${savedUrl.url} (Type: ${savedUrl.content_type})`);

    // Add embedding generation job to queue with API documentation info
    await this.embeddingsQueue.add('generate', {
      fileId: savedUrl.id,
      url: savedUrl.url,
      mimetype: apiDetectionInfo.isApiDocumentation ? 'application/api-documentation' : 'text/html',
      isPublishUrl: true,
      isApiDocumentation: apiDetectionInfo.isApiDocumentation,
      apiDetectionInfo: apiDetectionInfo.isApiDocumentation ? apiDetectionInfo : undefined
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: true,
    });

    return savedUrl;
  }

  async createPrivate(createPublishPrivateUrlDto: CreatePublishPrivateUrlDto): Promise<PublishUrl> {
    const publishUrl = this.publishUrlRepository.create({
      url_name: createPublishPrivateUrlDto.url_name,
      url_type: UrlType.PRIVATE, // Always use PRIVATE enum value for consistency
      url: createPublishPrivateUrlDto.url,
    });
    const savedUrl = await this.publishUrlRepository.save(publishUrl);

    await this.embeddingsQueue.add('generate', {
      fileId: savedUrl.id,
      url: savedUrl.url,
      mimetype: 'text/html',
      integration: createPublishPrivateUrlDto.integration,
      integration_email: createPublishPrivateUrlDto.integration_email,
      integration_api_token: createPublishPrivateUrlDto.integration_api_token,
      isPublishUrl: true,
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: true,
    });

    return savedUrl;
  }

  async findAll(): Promise<PublishUrl[]> {
    return this.publishUrlRepository.find({
      order: { created_at: 'DESC' },
    });
  }

  async findOne(id: string): Promise<PublishUrl> {
    const publishUrl = await this.publishUrlRepository.findOne({ where: { id } });
    if (!publishUrl) {
      throw new NotFoundException('Publish URL not found');
    }
    return publishUrl;
  }

  async remove(id: string): Promise<void> {
    const publishUrl = await this.publishUrlRepository.findOne({ where: { id } });
    if (!publishUrl) {
      throw new NotFoundException('Publish URL not found');
    }

    // Delete associated embeddings first
    await this.embeddingRepository.delete({ publishUrlId: id });

    // Then delete the publish URL
    await this.publishUrlRepository.remove(publishUrl);
  }

  async getEmbeddingProgress(id: string): Promise<number> {
    // First verify URL exists
    await this.findOne(id);

    // Get jobs in all relevant states
    const jobs = await this.embeddingsQueue.getJobs(['active', 'waiting', 'completed', 'failed']);
    const job = jobs.find(j => j.data.fileId === id);

    if (!job) {
      // If no job found, check if we have any embeddings
      const embeddings = await this.embeddingRepository.count({
        where: { publishUrlId: id },
      });

      return embeddings > 0 ? 100 : 0;
    }

    // Get job state
    const state = await job.getState();

    if (state === 'completed') {
      return 100;
    }

    if (state === 'failed') {
      return 0;
    }

    if (state === 'waiting') {
      return 0;
    }

    // For active jobs, get the actual progress from BullMQ
    if (state === 'active') {
      try {
        // Get the job progress (BullMQ stores this as a number 0-100)
        const progress = job.progress;

        // If progress is an object with percentage, extract it
        if (typeof progress === 'object' && progress !== null && 'percentage' in progress) {
          return Math.round(progress.percentage);
        }

        // If progress is a number, return it
        if (typeof progress === 'number') {
          return Math.round(progress);
        }

        // Fallback: calculate progress based on processed chunks
        const data = job.data;
        if (data.totalChunks && data.processedChunks) {
          return Math.round((data.processedChunks / data.totalChunks) * 100);
        }

        return 0;
      } catch (error) {
        console.error('Error getting job progress:', error);
        return 0;
      }
    }

    return 0;
  }

  async getEmbeddingUsage(id: string): Promise<TokenUsageResponseDto> {
    // Verify URL exists
    const publishUrl = await this.findOne(id);

    // Get all embeddings for this URL
    const embeddings = await this.embeddingRepository.find({
      where: { publishUrlId: id }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this URL');
    }

    // Get all token usage records for these embeddings
    const embeddingIds = embeddings.map(e => e.id);
    const tokenUsages = await this.tokenUsageRepository.find({
      where: {
        embeddingId: In(embeddingIds),
        tokenType: TokenType.EMBEDDING_INPUT
      },
      order: { timestamp: 'ASC' }
    });

    if (!tokenUsages.length) {
      throw new NotFoundException('No embedding token usage data found for this URL');
    }

    const totalTokens = tokenUsages.reduce((sum, u) => sum + u.tokensUsed, 0);

    return {
      fileId: id,
      totalTokensUsed: totalTokens,
      usageByType: [{
        tokenType: TokenType.EMBEDDING_INPUT,
        totalTokens,
        operationCount: tokenUsages.length,
        averageTokensPerOperation: Math.round(totalTokens / tokenUsages.length)
      }],
      firstUsageTime: tokenUsages[0].timestamp,
      lastUsageTime: tokenUsages[tokenUsages.length - 1].timestamp
    };
  }

  async getCompletionUsage(id: string): Promise<TokenUsageResponseDto> {
    // Verify URL exists
    const publishUrl = await this.findOne(id);

    // Get all embeddings for this URL
    const embeddings = await this.embeddingRepository.find({
      where: { publishUrlId: id }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this URL');
    }

    // Get all token usage records for these embeddings
    const embeddingIds = embeddings.map(e => e.id);
    const tokenUsages = await this.tokenUsageRepository.find({
      where: {
        embeddingId: In(embeddingIds),
        tokenType: In([TokenType.COMPLETION_INPUT, TokenType.COMPLETION_OUTPUT])
      },
      order: { timestamp: 'ASC' }
    });

    if (!tokenUsages.length) {
      throw new NotFoundException('No completion token usage data found for this URL');
    }

    // Calculate usage statistics by token type
    const usageByType = [TokenType.COMPLETION_INPUT, TokenType.COMPLETION_OUTPUT].map(tokenType => {
      const usagesOfType = tokenUsages.filter(u => u.tokenType === tokenType);
      const totalTokens = usagesOfType.reduce((sum, u) => sum + u.tokensUsed, 0);
      
      return {
        tokenType,
        totalTokens,
        operationCount: usagesOfType.length,
        averageTokensPerOperation: usagesOfType.length ? Math.round(totalTokens / usagesOfType.length) : 0
      };
    }).filter(usage => usage.operationCount > 0);

    return {
      fileId: id,
      totalTokensUsed: tokenUsages.reduce((sum, u) => sum + u.tokensUsed, 0),
      usageByType,
      firstUsageTime: tokenUsages[0].timestamp,
      lastUsageTime: tokenUsages[tokenUsages.length - 1].timestamp
    };
  }

  async generateTestCases(id: string) {
    // First verify URL exists
    const publishUrl = await this.findOne(id);

    // Get embeddings for the URL
    const embeddings = await this.embeddingRepository.find({
      where: { publishUrlId: id },
      order: { createdAt: 'ASC' }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this URL');
    }

    // Combine all content
    const combinedContent = embeddings.map(e => e.content).join('\n\n');

    // Generate test cases using appropriate method based on content type
    let generatedTestCases: any[];
    if (publishUrl.content_type === UrlContentType.API_DOCUMENTATION) {
      this.logger.log(`Generating MCP-based API test cases for ${publishUrl.url}`);

      // Use MCP-based intelligent test generation for API documentation
      const apiId = this.mcpService.generateApiId(publishUrl.url);

      // Check if API documentation is available in MCP
      const hasApiData = await this.mcpService.hasApiDocumentation(apiId);

      if (hasApiData) {
        this.logger.log(`Using MCP-based intelligent test generation for API ${apiId}`);
        generatedTestCases = await this.openAIService.generateMcpApiTestCases(apiId, embeddings[0].id);
      } else {
        this.logger.warn(`API data not found in MCP for ${apiId}, falling back to content-based generation`);

        // Fallback to content-based generation
        const endpointCount = publishUrl.api_metadata?.endpoints || 0;
        if (endpointCount > 20) {
          this.logger.log(`Large API detected (${endpointCount} endpoints), using comprehensive test generation`);
          generatedTestCases = await this.openAIService.generateComprehensiveApiTestCases(
            combinedContent,
            embeddings[0].id,
            publishUrl.api_metadata
          );
        } else {
          generatedTestCases = await this.openAIService.generateApiTestCases(
            combinedContent,
            embeddings[0].id,
            publishUrl.api_metadata
          );
        }
      }
    } else {
      this.logger.log(`Generating general test cases for ${publishUrl.url}`);
      generatedTestCases = await this.openAIService.generateTestCases(combinedContent, embeddings[0].id);
    }

    // Store the generated test cases (flatten in case of nested arrays)
    const flattenedTestCases: any[] = Array.isArray(generatedTestCases[0])
      ? generatedTestCases.flat()
      : generatedTestCases;

    const testCaseEntities: TestCaseGeneration[] = [];

    for (const testCase of flattenedTestCases) {
      const entity = this.testCaseGenerationRepository.create({
        name: testCase.name,
        precondition: testCase.precondition,
        steps: testCase.steps,
        expectation: testCase.expectation,
        testType: testCase.testType,
        priority: testCase.priority,
        platform: testCase.platform,
        testCaseType: testCase.testCaseType,
        automationByAgentq: testCase.automationByAgentq,
        publishUrlId: id
      });
      testCaseEntities.push(entity);
    }

    // Save all test cases
    await this.testCaseGenerationRepository.save(testCaseEntities);

    return testCaseEntities;
  }

  async generateMoreTestCases(id: string) {
    // First verify URL exists
    const url = await this.publishUrlRepository.findOne({
      where: { id }
    });

    if (!url) {
      throw new NotFoundException('URL not found');
    }

    // Get existing test cases to avoid duplicates
    const existingTestCases = await this.testCaseGenerationRepository.find({
      where: { publishUrlId: id }
    });

    // Get embeddings for the URL
    const embeddings = await this.embeddingRepository.find({
      where: { publishUrlId: id },
      order: { createdAt: 'ASC' }
    });

    if (!embeddings.length) {
      throw new NotFoundException('No embeddings found for this URL');
    }

    // Combine all content
    const combinedContent = embeddings.map(e => e.content).join('\n\n');

    // Extract existing test scenarios
    const existingScenarios = existingTestCases.map(tc => ({
      name: tc.name,
      type: tc.testType,
      platform: tc.platform,
      automationByAgentq: tc.automationByAgentq
    }));

    // Generate new test cases with context about existing ones
    let generatedTestCases;
    if (url.content_type === UrlContentType.API_DOCUMENTATION) {
      this.logger.log(`Generating additional API-specific test cases for ${url.url}`);
      // For API documentation, we can use the regular API test case generation
      // as it will naturally avoid duplicates by covering different scenarios
      generatedTestCases = await this.openAIService.generateApiTestCases(
        combinedContent,
        embeddings[0].id,
        url.api_metadata
      );
    } else {
      generatedTestCases = await this.openAIService.generateMoreTestCases(
        combinedContent,
        embeddings[0].id,
        existingScenarios
      );
    }

    // Store the generated test cases
    const testCases = generatedTestCases.map(testCase => 
      this.testCaseGenerationRepository.create({
        ...testCase,
        publishUrlId: id
      })
    );

    // Save all test cases
    await this.testCaseGenerationRepository.save(testCases);

    return testCases;
  }

  async getGeneratedTestCases(id: string) {
    // First verify URL exists
    const publishUrl = await this.findOne(id);

    // Get all test cases for this URL
    const testCases = await this.testCaseGenerationRepository.find({
      where: { publishUrlId: id },
      order: { createdAt: 'ASC' }
    });

    if (!testCases.length) {
      throw new NotFoundException('No generated test cases found for this URL');
    }

    return testCases;
  }

  async updateGeneratedTestCase(publishUrlId: string, id: string, updateData: Partial<TestCaseGeneration>) {
    // First verify URL exists
    await this.findOne(publishUrlId);

    // Find the test case
    const testCase = await this.testCaseGenerationRepository.findOne({
      where: { id, publishUrlId }
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Update the test case
    Object.assign(testCase, updateData);
    return this.testCaseGenerationRepository.save(testCase);
  }

  async deleteGeneratedTestCase(publishUrlId: string, id: string) {
    // First verify URL exists
    await this.findOne(publishUrlId);

    // Find the test case
    const testCase = await this.testCaseGenerationRepository.findOne({
      where: { id, publishUrlId }
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    // Delete the test case
    await this.testCaseGenerationRepository.remove(testCase);
  }
}
