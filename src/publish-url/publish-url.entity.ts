import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum UrlType {
  PRIVATE = 'private',
  PUBLIC = 'public'
}

export enum UrlContentType {
  WEBSITE = 'website',
  API_DOCUMENTATION = 'api_documentation'
}

@Entity('publish_url')
export class PublishUrl {
  @ApiProperty({
    description: 'The unique identifier of the publish URL',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Name of the URL',
    example: 'My Documentation'
  })
  @Column()
  url_name: string;

  @ApiProperty({
    description: 'Type of URL (private/public)',
    enum: UrlType,
    example: UrlType.PUBLIC
  })
  @Column({
    type: 'enum',
    enum: UrlType,
    default: UrlType.PUBLIC
  })
  url_type: UrlType;

  @ApiProperty({
    description: 'The actual URL link',
    example: 'https://example.com/docs'
  })
  @Column()
  url: string;

  @ApiProperty({
    description: 'Content type of the URL (website or api_documentation)',
    enum: UrlContentType,
    example: UrlContentType.WEBSITE
  })
  @Column({
    type: 'enum',
    enum: UrlContentType,
    default: UrlContentType.WEBSITE
  })
  content_type: UrlContentType;

  @ApiProperty({
    description: 'API documentation metadata (JSON)',
    example: '{"type": "swagger", "version": "3.0", "endpoints": 15}'
  })
  @Column({ type: 'json', nullable: true })
  api_metadata: any;

  @ApiProperty({
    description: 'When the URL was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  created_at: Date;

  @ApiProperty({
    description: 'When the URL was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updated_at: Date;
}