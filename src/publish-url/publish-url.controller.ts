import { Controller, Get, Post, Delete, Patch, Body, Param, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PublishUrlService } from './publish-url.service';
import { PublishUrl } from './publish-url.entity';
import { CreatePublishUrlDto } from './dto/create-publish-url.dto';
import { CreatePublishPrivateUrlDto } from './dto/create-publish-private-url.dto';
import { TokenUsageResponseDto } from '../file-upload/dto/token-usage-response.dto';
import { TestCaseGeneration } from '../file-upload/test-case-generation.entity';

@ApiTags('publish-url')
@Controller('publish_url')
export class PublishUrlController {
  constructor(private readonly publishUrlService: PublishUrlService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new publish URL' })
  @ApiResponse({ status: 201, description: 'URL created successfully', type: PublishUrl })
  async create(@Body() createPublishUrlDto: CreatePublishUrlDto): Promise<PublishUrl> {
    return this.publishUrlService.create(createPublishUrlDto);
  }

  @Post('private')
  @ApiOperation({ summary: 'Create a new private publish URL' })
  @ApiResponse({ status: 201, description: 'Private URL created successfully', type: PublishUrl })
  async createPrivate(@Body() createPublishPrivateUrlDto: CreatePublishPrivateUrlDto): Promise<PublishUrl> {
    return this.publishUrlService.createPrivate(createPublishPrivateUrlDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all publish URLs' })
  @ApiResponse({ status: 200, description: 'List of publish URLs', type: [PublishUrl] })
  async findAll(): Promise<PublishUrl[]> {
    return this.publishUrlService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a publish URL by ID' })
  @ApiResponse({ status: 200, description: 'URL found', type: PublishUrl })
  @ApiResponse({ status: 404, description: 'URL not found' })
    async findOne(@Param('id') id: string): Promise<PublishUrl> {
        const url = await this.publishUrlService.findOne(id);
        if (!url) {
            throw new NotFoundException('Publish URL not found');
        }
        return url;
    }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a publish URL' })
  @ApiResponse({ status: 200, description: 'URL deleted successfully' })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async remove(@Param('id') id: string): Promise<void> {
    try {
      await this.publishUrlService.remove(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException('Publish URL not found');
      }
      throw error;
    }
  }

  @Get(':id/progress')
  @ApiOperation({ 
    summary: 'Get embedding generation progress',
    description: 'Get the progress of embedding generation for a specific URL.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Progress percentage', 
    schema: {
      type: 'object',
      properties: {
        progress: {
          type: 'number',
          description: 'Progress percentage (0-100)',
          example: 45.5
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async getProgress(@Param('id') id: string): Promise<{ progress: number }> {
    // Verify URL exists
    await this.publishUrlService.findOne(id);
    const progress = await this.publishUrlService.getEmbeddingProgress(id);
    return { progress };
  }

  @Get(':id/embedding-usage')
  @ApiOperation({ 
    summary: 'Get embedding token usage',
    description: 'Get embedding token usage statistics for a specific publish URL'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Token usage statistics',
    type: TokenUsageResponseDto
  })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async getEmbeddingUsage(@Param('id') id: string): Promise<TokenUsageResponseDto> {
    return this.publishUrlService.getEmbeddingUsage(id);
  }

  @Get(':id/completion-usage')
  @ApiOperation({ 
    summary: 'Get completion token usage',
    description: 'Get completion token usage statistics for a specific publish URL'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Token usage statistics',
    type: TokenUsageResponseDto
  })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async getCompletionUsage(@Param('id') id: string): Promise<TokenUsageResponseDto> {
    return this.publishUrlService.getCompletionUsage(id);
  }

  @Post(':id/ai-generate-test-cases')
  @ApiOperation({ 
    summary: 'Generate test cases using AI',
    description: 'Generate test cases from URL content using OpenAI'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Generated test cases',
    type: [TestCaseGeneration]
  })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async generateTestCases(@Param('id') id: string) {
    return this.publishUrlService.generateTestCases(id);
  }

  @Post(':id/ai-generate-more-test-cases')
  @ApiOperation({ 
    summary: 'Generate additional test cases using AI',
    description: 'Generate more test cases from URL content using OpenAI, avoiding duplicates'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Generated additional test cases',
    type: [TestCaseGeneration]
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async generateMoreTestCases(@Param('id') id: string) {
    return this.publishUrlService.generateMoreTestCases(id);
  }

  @Get(':id/ai-generate-test-cases')
  @ApiOperation({ 
    summary: 'Get generated test cases',
    description: 'Get the generated test cases for a specific URL'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Generated test cases retrieved successfully',
    type: [TestCaseGeneration]
  })
  @ApiResponse({ status: 404, description: 'URL not found' })
  async getGeneratedTestCases(@Param('id') id: string) {
    return this.publishUrlService.getGeneratedTestCases(id);
  }

  @Patch(':publishUrlId/ai-generate-test-cases/:id')
  @ApiOperation({ 
    summary: 'Update a generated test case',
    description: 'Update a specific generated test case by ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test case updated successfully',
    type: TestCaseGeneration
  })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async updateGeneratedTestCase(
    @Param('publishUrlId') publishUrlId: string,
    @Param('id') id: string,
    @Body() updateData: Partial<TestCaseGeneration>
  ) {
    return this.publishUrlService.updateGeneratedTestCase(publishUrlId, id, updateData);
  }

  @Delete(':publishUrlId/ai-generate-test-cases/:id')
  @ApiOperation({ 
    summary: 'Delete a generated test case',
    description: 'Delete a specific generated test case by ID'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test case deleted successfully'
  })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async deleteGeneratedTestCase(
    @Param('publishUrlId') publishUrlId: string,
    @Param('id') id: string
  ) {
    return this.publishUrlService.deleteGeneratedTestCase(publishUrlId, id);
  }
}