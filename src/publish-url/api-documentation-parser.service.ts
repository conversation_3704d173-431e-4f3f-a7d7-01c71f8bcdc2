import { Injectable, Logger } from '@nestjs/common';
import { ApiDocumentationInfo } from './api-documentation-detector.service';
import { ApiDocumentationMcpService } from '../mcp/api-documentation-mcp.service';

export interface ParsedApiEndpoint {
  path: string;
  method: string;
  summary?: string;
  description?: string;
  parameters?: ParsedApiParameter[];
  requestBody?: any;
  responses?: any;
  tags?: string[];
}

export interface ParsedApiParameter {
  name: string;
  in: 'query' | 'path' | 'header' | 'cookie' | 'body';
  type: string;
  required: boolean;
  description?: string;
  example?: any;
}

export interface ParsedApiDocumentation {
  title: string;
  description?: string;
  version?: string;
  baseUrl?: string;
  endpoints: ParsedApiEndpoint[];
  schemas?: any;
  tags?: string[];
  servers?: any[];
  metadata: any;
}

@Injectable()
export class ApiDocumentationParserService {
  private readonly logger = new Logger(ApiDocumentationParserService.name);

  constructor(private mcpService: ApiDocumentationMcpService) {}

  /**
   * Parse API documentation based on detected type
   */
  async parseApiDocumentation(url: string, detectionInfo: ApiDocumentationInfo): Promise<ParsedApiDocumentation> {
    try {
      let parsedApi: ParsedApiDocumentation;

      switch (detectionInfo.type) {
        case 'swagger':
        case 'openapi':
          parsedApi = await this.parseOpenApiSpec(url, detectionInfo);
          break;
        case 'postman':
          parsedApi = await this.parsePostmanCollection(url, detectionInfo);
          break;
        case 'redoc':
          parsedApi = await this.parseRedocDocumentation(url, detectionInfo);
          break;
        default:
          parsedApi = await this.parseGenericApiDocumentation(url, detectionInfo);
          break;
      }

      // Store the parsed API documentation in MCP for intelligent access
      await this.storeInMcp(url, parsedApi);

      return parsedApi;
    } catch (error) {
      this.logger.error(`Error parsing API documentation for ${url}:`, error);
      throw new Error(`Failed to parse API documentation: ${error.message}`);
    }
  }

  /**
   * Store parsed API documentation in MCP server for intelligent access
   */
  private async storeInMcp(url: string, parsedApi: ParsedApiDocumentation): Promise<void> {
    try {
      const apiId = this.mcpService.generateApiId(url);
      await this.mcpService.storeApiDocumentation(apiId, parsedApi);
      this.logger.log(`Stored API documentation in MCP with ID: ${apiId}`);
    } catch (error) {
      this.logger.error(`Failed to store API documentation in MCP:`, error);
      // Don't throw error here - MCP storage is optional enhancement
    }
  }

  /**
   * Parse OpenAPI/Swagger specification
   */
  private async parseOpenApiSpec(url: string, detectionInfo: ApiDocumentationInfo): Promise<ParsedApiDocumentation> {
    let specUrl = url;

    // If it's a Swagger UI page, try to find the spec URL
    if (detectionInfo.metadata?.detectedBy === 'html_content' ||
        detectionInfo.metadata?.detectedBy === 'url_pattern') {

      // Check if we have a likely spec URL from detection
      if (detectionInfo.metadata?.likelySpecUrl) {
        specUrl = detectionInfo.metadata.likelySpecUrl;
        this.logger.log(`Using likely spec URL: ${specUrl}`);
      } else {
        specUrl = await this.findSwaggerSpecUrl(url);
      }
    }

    try {
      this.logger.log(`Attempting to fetch spec from: ${specUrl}`);

      const response = await fetch(specUrl, {
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'User-Agent': 'AgentQ-API-Parser/1.0',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      const responseText = await response.text();

      // Check if we got HTML instead of JSON (common with auth-protected APIs)
      if (contentType?.includes('text/html') || responseText.trim().startsWith('<')) {
        this.logger.warn(`Spec URL ${specUrl} returned HTML instead of JSON. This might indicate:
          1. Authentication required
          2. CORS restrictions
          3. Different spec URL pattern
          Attempting alternative approaches...`);

        // Try alternative spec URL patterns
        const alternativeUrls = this.generateAlternativeSpecUrls(url);
        for (const altUrl of alternativeUrls) {
          try {
            this.logger.log(`Trying alternative spec URL: ${altUrl}`);
            const altResponse = await fetch(altUrl, {
              headers: {
                'Accept': 'application/json, text/plain, */*',
                'User-Agent': 'AgentQ-API-Parser/1.0',
              },
            });

            if (altResponse.ok) {
              const altContentType = altResponse.headers.get('content-type');
              const altResponseText = await altResponse.text();

              if (!altContentType?.includes('text/html') && !altResponseText.trim().startsWith('<')) {
                const spec = JSON.parse(altResponseText);
                this.logger.log(`Successfully found spec at alternative URL: ${altUrl}`);
                return this.parseOpenApiSpecFromJson(spec, altUrl, url);
              }
            }
          } catch (error) {
            this.logger.debug(`Alternative URL ${altUrl} failed: ${error.message}`);
          }
        }

        // If all alternatives fail, try to extract spec from Swagger UI page
        return this.extractSpecFromSwaggerUI(url);
      }

      const spec = JSON.parse(responseText);
      return this.parseOpenApiSpecFromJson(spec, specUrl, url);

    } catch (error) {
      this.logger.error(`Failed to fetch spec from ${specUrl}: ${error.message}`);

      // Fallback: try to extract spec from Swagger UI page
      return this.extractSpecFromSwaggerUI(url);
    }
  }

  /**
   * Generate alternative spec URL patterns to try
   */
  private generateAlternativeSpecUrls(originalUrl: string): string[] {
    const url = new URL(originalUrl);
    const baseUrl = `${url.protocol}//${url.host}`;

    const alternatives = [
      `${baseUrl}/api/swagger/v1/swagger.json`,
      `${baseUrl}/api/swagger/v1`,
      `${baseUrl}/swagger/v1/swagger.json`,
      `${baseUrl}/swagger/v1`,
      `${baseUrl}/api/v1/swagger.json`,
      `${baseUrl}/api/docs/swagger.json`,
      `${baseUrl}/docs/swagger.json`,
      `${baseUrl}/swagger.json`,
      `${baseUrl}/api/swagger.json`,
      `${baseUrl}/openapi.json`,
      `${baseUrl}/api/openapi.json`,
    ];

    return alternatives;
  }

  /**
   * Extract OpenAPI spec from Swagger UI page by parsing the HTML
   */
  private async extractSpecFromSwaggerUI(url: string): Promise<ParsedApiDocumentation> {
    try {
      this.logger.log(`Attempting to extract spec from Swagger UI page: ${url}`);

      const response = await fetch(url);
      const html = await response.text();

      // Look for common patterns where the spec URL is embedded
      const specUrlPatterns = [
        /url:\s*["']([^"']+)["']/g,
        /configUrl:\s*["']([^"']+)["']/g,
        /"url":\s*["']([^"']+)["']/g,
        /swagger-ui-bundle\.js.*?url:\s*["']([^"']+)["']/g,
      ];

      for (const pattern of specUrlPatterns) {
        const matches = [...html.matchAll(pattern)];
        for (const match of matches) {
          const potentialSpecUrl = match[1];
          if (potentialSpecUrl && (potentialSpecUrl.includes('swagger') || potentialSpecUrl.includes('openapi'))) {
            try {
              // Make the URL absolute if it's relative
              const absoluteUrl = potentialSpecUrl.startsWith('http')
                ? potentialSpecUrl
                : new URL(potentialSpecUrl, url).toString();

              this.logger.log(`Found potential spec URL in HTML: ${absoluteUrl}`);

              const specResponse = await fetch(absoluteUrl);
              if (specResponse.ok) {
                const specText = await specResponse.text();
                if (!specText.trim().startsWith('<')) {
                  const spec = JSON.parse(specText);
                  this.logger.log(`Successfully extracted spec from Swagger UI page`);
                  return this.parseOpenApiSpecFromJson(spec, absoluteUrl, url);
                }
              }
            } catch (error) {
              this.logger.debug(`Failed to fetch extracted spec URL: ${error.message}`);
            }
          }
        }
      }

      // If we can't find the spec URL, create a minimal parsed documentation from the HTML
      this.logger.warn(`Could not extract OpenAPI spec from Swagger UI. Creating minimal documentation from HTML.`);
      return this.createMinimalApiDocFromHtml(html, url);

    } catch (error) {
      this.logger.error(`Failed to extract spec from Swagger UI: ${error.message}`);
      throw new Error(`Unable to parse API documentation from ${url}`);
    }
  }

  /**
   * Create minimal API documentation from HTML when spec extraction fails
   */
  private createMinimalApiDocFromHtml(html: string, url: string): ParsedApiDocumentation {
    // Extract basic information from HTML
    const titleMatch = html.match(/<title>([^<]+)<\/title>/i);
    const title = titleMatch ? titleMatch[1] : 'API Documentation';

    return {
      title,
      description: `API documentation extracted from Swagger UI at ${url}`,
      version: 'unknown',
      baseUrl: new URL(url).origin,
      endpoints: [], // Will be empty, but MCP system can still store this
      schemas: {},
      tags: [],
      servers: [{ url: new URL(url).origin }],
      metadata: {
        source: 'swagger-ui-html',
        originalUrl: url,
        extractionMethod: 'html-fallback',
        note: 'Spec extraction failed, minimal documentation created'
      }
    };
  }

  /**
   * Parse OpenAPI spec from JSON object
   */
  private parseOpenApiSpecFromJson(spec: any, specUrl: string, originalUrl?: string): ParsedApiDocumentation {

    const endpoints: ParsedApiEndpoint[] = [];
    
    if (spec.paths) {
      for (const [path, pathItem] of Object.entries(spec.paths as any)) {
        const methods = ['get', 'post', 'put', 'patch', 'delete', 'options', 'head'];
        
        for (const method of methods) {
          const operation = (pathItem as any)[method];
          if (operation) {
            endpoints.push({
              path,
              method: method.toUpperCase(),
              summary: operation.summary,
              description: operation.description,
              parameters: this.parseOpenApiParameters(operation.parameters, (pathItem as any).parameters),
              requestBody: operation.requestBody,
              responses: operation.responses,
              tags: operation.tags
            });
          }
        }
      }
    }

    return {
      title: spec.info?.title || 'API Documentation',
      description: spec.info?.description,
      version: spec.info?.version || spec.openapi || spec.swagger,
      baseUrl: this.extractBaseUrl(spec),
      endpoints,
      schemas: spec.components?.schemas || spec.definitions,
      tags: spec.tags?.map((tag: any) => tag.name) || [],
      servers: spec.servers,
      metadata: {
        type: 'openapi',
        originalUrl: originalUrl || specUrl,
        specUrl,
        endpointCount: endpoints.length
      }
    };
  }

  /**
   * Parse Postman collection
   */
  private async parsePostmanCollection(url: string, detectionInfo: ApiDocumentationInfo): Promise<ParsedApiDocumentation> {
    const response = await fetch(url);
    const collection = await response.json();

    const endpoints: ParsedApiEndpoint[] = [];
    
    if (collection.item) {
      this.extractPostmanEndpoints(collection.item, endpoints);
    }

    return {
      title: collection.info?.name || 'Postman Collection',
      description: collection.info?.description,
      version: collection.info?.version,
      baseUrl: this.extractPostmanBaseUrl(collection),
      endpoints,
      metadata: {
        type: 'postman',
        originalUrl: url,
        endpointCount: endpoints.length
      }
    };
  }

  /**
   * Parse ReDoc documentation
   */
  private async parseRedocDocumentation(url: string, detectionInfo: ApiDocumentationInfo): Promise<ParsedApiDocumentation> {
    // ReDoc is usually a wrapper around OpenAPI spec
    // Try to find the spec URL from the HTML
    const specUrl = await this.findRedocSpecUrl(url);
    
    if (specUrl) {
      return await this.parseOpenApiSpec(specUrl, { ...detectionInfo, type: 'openapi' });
    }

    // Fallback to generic parsing
    return await this.parseGenericApiDocumentation(url, detectionInfo);
  }

  /**
   * Parse generic API documentation
   */
  private async parseGenericApiDocumentation(url: string, detectionInfo: ApiDocumentationInfo): Promise<ParsedApiDocumentation> {
    const response = await fetch(url);
    const html = await response.text();
    
    // Use cheerio to extract information from HTML
    const cheerio = await import('cheerio');
    const $ = cheerio.load(html);
    
    const title = $('title').text() || $('h1').first().text() || 'API Documentation';
    const description = $('meta[name="description"]').attr('content') || 
                       $('.description').first().text() || 
                       $('p').first().text();

    // Try to extract endpoint information from HTML structure
    const endpoints: ParsedApiEndpoint[] = [];
    
    // Look for common patterns in API documentation
    $('[data-method], .endpoint, .api-endpoint, .method').each((_, element) => {
      const $el = $(element);
      const method = $el.attr('data-method') || 
                    $el.find('.method, .http-method').text().trim().toUpperCase() ||
                    this.extractMethodFromText($el.text());
      const path = $el.attr('data-path') || 
                  $el.find('.path, .endpoint-path').text().trim() ||
                  this.extractPathFromText($el.text());
      
      if (method && path) {
        endpoints.push({
          path,
          method,
          summary: $el.find('.summary, .title, h3, h4').first().text().trim(),
          description: $el.find('.description, .desc, p').first().text().trim()
        });
      }
    });

    return {
      title,
      description,
      baseUrl: this.extractBaseUrlFromHtml($),
      endpoints,
      metadata: {
        type: 'generic',
        originalUrl: url,
        endpointCount: endpoints.length,
        detectionInfo
      }
    };
  }

  /**
   * Parse OpenAPI parameters
   */
  private parseOpenApiParameters(operationParams: any[] = [], pathParams: any[] = []): ParsedApiParameter[] {
    const allParams = [...(pathParams || []), ...(operationParams || [])];
    
    return allParams.map(param => ({
      name: param.name,
      in: param.in,
      type: param.schema?.type || param.type || 'string',
      required: param.required || false,
      description: param.description,
      example: param.example || param.schema?.example
    }));
  }

  /**
   * Extract endpoints from Postman collection items
   */
  private extractPostmanEndpoints(items: any[], endpoints: ParsedApiEndpoint[]): void {
    for (const item of items) {
      if (item.request) {
        const method = item.request.method || 'GET';
        const url = typeof item.request.url === 'string' ? 
                   item.request.url : 
                   item.request.url?.raw || '';
        
        endpoints.push({
          path: this.extractPathFromUrl(url),
          method: method.toUpperCase(),
          summary: item.name,
          description: item.request.description
        });
      } else if (item.item && Array.isArray(item.item)) {
        this.extractPostmanEndpoints(item.item, endpoints);
      }
    }
  }

  /**
   * Extract base URL from OpenAPI spec
   */
  private extractBaseUrl(spec: any): string | undefined {
    if (spec.servers && spec.servers.length > 0) {
      return spec.servers[0].url;
    }
    
    if (spec.host) {
      const scheme = spec.schemes?.[0] || 'https';
      const basePath = spec.basePath || '';
      return `${scheme}://${spec.host}${basePath}`;
    }
    
    return undefined;
  }

  /**
   * Extract base URL from Postman collection
   */
  private extractPostmanBaseUrl(collection: any): string | undefined {
    if (collection.variable) {
      const baseUrlVar = collection.variable.find((v: any) => 
        v.key === 'baseUrl' || v.key === 'base_url' || v.key === 'host'
      );
      if (baseUrlVar) {
        return baseUrlVar.value;
      }
    }
    
    return undefined;
  }

  /**
   * Find Swagger spec URL from Swagger UI page
   */
  private async findSwaggerSpecUrl(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      const html = await response.text();

      // Look for spec URL in common patterns
      const specUrlMatch = html.match(/url:\s*["']([^"']+)["']/i) ||
                          html.match(/spec:\s*["']([^"']+)["']/i) ||
                          html.match(/configUrl:\s*["']([^"']+)["']/i);

      if (specUrlMatch) {
        const specUrl = specUrlMatch[1];
        return new URL(specUrl, url).href;
      }

      // Generic handling for swagger dashboard patterns
      if (url.includes('/api/swagger') && (url.includes('index.html') || url.includes('#'))) {
        // Try common API spec URL patterns
        const baseUrl = url.replace(/\/swagger.*$/, '');
        const commonPatterns = [
          `${baseUrl}/swagger/v1`,
          `${baseUrl}/swagger/v1/swagger.json`,
          `${baseUrl}/swagger.json`,
          `${baseUrl}/api/swagger.json`,
        ];

        // Try each pattern and return the first working one
        for (const specUrl of commonPatterns) {
          try {
            const specResponse = await fetch(specUrl, { method: 'HEAD' });
            if (specResponse.ok) {
              this.logger.log(`Found spec URL via pattern matching: ${specUrl}`);
              return specUrl;
            }
          } catch (error) {
            this.logger.debug(`Spec URL pattern not accessible: ${specUrl}`);
          }
        }
      }

      // Try common spec URL patterns based on the base URL
      const baseUrl = new URL(url).origin + new URL(url).pathname.replace(/\/[^\/]*$/, '');
      const commonSpecPaths = [
        '/swagger.json',
        '/swagger/v1',
        '/api-docs',
        '/openapi.json',
        '/v2/api-docs',
        '/swagger/doc.json'
      ];

      for (const specPath of commonSpecPaths) {
        try {
          const testUrl = baseUrl + specPath;
          const testResponse = await fetch(testUrl, { method: 'HEAD' });
          if (testResponse.ok) {
            this.logger.log(`Found spec URL via pattern matching: ${testUrl}`);
            return testUrl;
          }
        } catch (error) {
          // Continue to next pattern
        }
      }
    } catch (error) {
      this.logger.warn(`Could not find spec URL for ${url}:`, error);
    }

    return url;
  }

  /**
   * Find ReDoc spec URL from ReDoc page
   */
  private async findRedocSpecUrl(url: string): Promise<string | null> {
    try {
      const response = await fetch(url);
      const html = await response.text();
      
      const specUrlMatch = html.match(/spec-url=["']([^"']+)["']/i) ||
                          html.match(/specUrl:\s*["']([^"']+)["']/i);
      
      if (specUrlMatch) {
        return new URL(specUrlMatch[1], url).href;
      }
    } catch (error) {
      this.logger.warn(`Could not find spec URL for ReDoc ${url}:`, error);
    }
    
    return null;
  }

  /**
   * Extract HTTP method from text
   */
  private extractMethodFromText(text: string): string | null {
    const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'];
    const upperText = text.toUpperCase();
    
    for (const method of methods) {
      if (upperText.includes(method)) {
        return method;
      }
    }
    
    return null;
  }

  /**
   * Extract API path from text
   */
  private extractPathFromText(text: string): string | null {
    const pathMatch = text.match(/\/[a-zA-Z0-9\/_\-\{\}]*/);
    return pathMatch ? pathMatch[0] : null;
  }

  /**
   * Extract path from full URL
   */
  private extractPathFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname;
    } catch {
      // If URL parsing fails, try to extract path manually
      const pathMatch = url.match(/https?:\/\/[^\/]+(.*)$/);
      return pathMatch ? pathMatch[1] : url;
    }
  }

  /**
   * Extract base URL from HTML using cheerio
   */
  private extractBaseUrlFromHtml($: any): string | undefined {
    const baseUrl = $('meta[name="api-base-url"]').attr('content') ||
                   $('[data-base-url]').attr('data-base-url') ||
                   $('.base-url').text().trim();
    
    return baseUrl || undefined;
  }
}
