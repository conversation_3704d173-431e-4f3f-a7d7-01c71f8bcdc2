import { Injectable, Logger } from '@nestjs/common';

export interface ApiDocumentationInfo {
  isApiDocumentation: boolean;
  type?: 'swagger' | 'openapi' | 'postman' | 'insomnia' | 'redoc' | 'apiblueprint' | 'raml' | 'unknown';
  version?: string;
  title?: string;
  baseUrl?: string;
  endpoints?: number;
  metadata?: any;
}

@Injectable()
export class ApiDocumentationDetectorService {
  private readonly logger = new Logger(ApiDocumentationDetectorService.name);

  /**
   * Detect if a URL points to API documentation
   */
  async detectApiDocumentation(url: string): Promise<ApiDocumentationInfo> {
    try {
      // First check URL patterns
      const urlPatternResult = this.checkUrlPatterns(url);
      if (urlPatternResult.isApiDocumentation) {
        this.logger.log(`API documentation detected by URL pattern: ${url}`);
        return urlPatternResult;
      }

      // Then check content
      const contentResult = await this.checkContent(url);
      if (contentResult.isApiDocumentation) {
        this.logger.log(`API documentation detected by content analysis: ${url}`);
        return contentResult;
      }

      return { isApiDocumentation: false };
    } catch (error) {
      this.logger.error(`Error detecting API documentation for ${url}:`, error);
      return { isApiDocumentation: false };
    }
  }

  /**
   * Check URL patterns for common API documentation indicators
   */
  private checkUrlPatterns(url: string): ApiDocumentationInfo {
    const urlLower = url.toLowerCase();

    // Swagger/OpenAPI patterns
    if (urlLower.includes('/swagger') ||
        urlLower.includes('/api-docs') ||
        urlLower.includes('/openapi') ||
        urlLower.includes('/docs/api') ||
        urlLower.includes('/api/docs') ||
        urlLower.includes('/swagger-ui') ||
        urlLower.includes('/redoc')) {

      // Enhanced metadata for swagger patterns
      let metadata: any = { detectedBy: 'url_pattern' };

      // Generic handling for swagger dashboard patterns
      if (urlLower.includes('/swagger') && (urlLower.includes('index.html') || urlLower.includes('#'))) {
        metadata = {
          detectedBy: 'url_pattern',
          platform: 'swagger_ui',
          likelySpecUrl: url.replace(/\/swagger.*$/, '/swagger/v1')
        };
      }

      return {
        isApiDocumentation: true,
        type: urlLower.includes('/redoc') ? 'redoc' : 'swagger',
        metadata
      };
    }

    // Postman documentation patterns
    if (urlLower.includes('documenter.getpostman.com') ||
        urlLower.includes('postman.com/api-documentation') ||
        urlLower.includes('postman.com/collections')) {
      return {
        isApiDocumentation: true,
        type: 'postman',
        metadata: { detectedBy: 'url_pattern' }
      };
    }

    // Insomnia patterns
    if (urlLower.includes('insomnia.rest') && urlLower.includes('/docs')) {
      return {
        isApiDocumentation: true,
        type: 'insomnia',
        metadata: { detectedBy: 'url_pattern' }
      };
    }

    // API Blueprint patterns
    if (urlLower.includes('apiary.io') || urlLower.includes('apiblueprint.org')) {
      return {
        isApiDocumentation: true,
        type: 'apiblueprint',
        metadata: { detectedBy: 'url_pattern' }
      };
    }

    // RAML patterns
    if (urlLower.includes('raml.org') || urlLower.includes('/raml/')) {
      return {
        isApiDocumentation: true,
        type: 'raml',
        metadata: { detectedBy: 'url_pattern' }
      };
    }

    return { isApiDocumentation: false };
  }

  /**
   * Check content for API documentation indicators
   */
  private async checkContent(url: string): Promise<ApiDocumentationInfo> {
    try {
      // First check content type
      const headResponse = await fetch(url, { method: 'HEAD' });
      const contentType = headResponse.headers.get('content-type') || '';

      // Check for JSON content (might be OpenAPI spec)
      if (contentType.includes('application/json')) {
        const jsonResult = await this.checkJsonContent(url);
        if (jsonResult.isApiDocumentation) {
          return jsonResult;
        }
      }

      // Check HTML content
      if (contentType.includes('text/html')) {
        const htmlResult = await this.checkHtmlContent(url);
        if (htmlResult.isApiDocumentation) {
          return htmlResult;
        }
      }

      return { isApiDocumentation: false };
    } catch (error) {
      this.logger.warn(`Error checking content for ${url}:`, error);
      return { isApiDocumentation: false };
    }
  }

  /**
   * Check JSON content for OpenAPI/Swagger specifications
   */
  private async checkJsonContent(url: string): Promise<ApiDocumentationInfo> {
    try {
      const response = await fetch(url);
      const json = await response.json();

      // Check for OpenAPI 3.x
      if (json.openapi && typeof json.openapi === 'string') {
        return {
          isApiDocumentation: true,
          type: 'openapi',
          version: json.openapi,
          title: json.info?.title,
          baseUrl: json.servers?.[0]?.url,
          endpoints: this.countEndpoints(json.paths),
          metadata: {
            detectedBy: 'json_content',
            spec: 'openapi',
            version: json.openapi
          }
        };
      }

      // Check for Swagger 2.0
      if (json.swagger && json.swagger === '2.0') {
        return {
          isApiDocumentation: true,
          type: 'swagger',
          version: json.swagger,
          title: json.info?.title,
          baseUrl: json.host ? `${json.schemes?.[0] || 'https'}://${json.host}${json.basePath || ''}` : undefined,
          endpoints: this.countEndpoints(json.paths),
          metadata: {
            detectedBy: 'json_content',
            spec: 'swagger',
            version: json.swagger
          }
        };
      }

      // Check for Postman collection
      if (json.info && json.item && json.info.schema && 
          json.info.schema.includes('postman')) {
        return {
          isApiDocumentation: true,
          type: 'postman',
          title: json.info.name,
          endpoints: this.countPostmanEndpoints(json.item),
          metadata: {
            detectedBy: 'json_content',
            spec: 'postman_collection'
          }
        };
      }

      return { isApiDocumentation: false };
    } catch (error) {
      this.logger.warn(`Error parsing JSON content from ${url}:`, error);
      return { isApiDocumentation: false };
    }
  }

  /**
   * Check HTML content for API documentation indicators
   */
  private async checkHtmlContent(url: string): Promise<ApiDocumentationInfo> {
    try {
      const response = await fetch(url);
      const html = await response.text();
      const htmlLower = html.toLowerCase();

      // Check for Swagger UI
      if (htmlLower.includes('swagger-ui') || 
          htmlLower.includes('swaggerui') ||
          htmlLower.includes('"swagger"') ||
          htmlLower.includes('swagger-ui-bundle')) {
        return {
          isApiDocumentation: true,
          type: 'swagger',
          metadata: {
            detectedBy: 'html_content',
            indicators: ['swagger-ui']
          }
        };
      }

      // Check for ReDoc
      if (htmlLower.includes('redoc') || htmlLower.includes('redoc-cli')) {
        return {
          isApiDocumentation: true,
          type: 'redoc',
          metadata: {
            detectedBy: 'html_content',
            indicators: ['redoc']
          }
        };
      }

      // Check for common API documentation keywords
      const apiKeywords = [
        'api documentation',
        'rest api',
        'api reference',
        'api endpoints',
        'openapi',
        'api specification'
      ];

      const foundKeywords = apiKeywords.filter(keyword => htmlLower.includes(keyword));
      if (foundKeywords.length >= 2) {
        return {
          isApiDocumentation: true,
          type: 'unknown',
          metadata: {
            detectedBy: 'html_content',
            indicators: foundKeywords
          }
        };
      }

      return { isApiDocumentation: false };
    } catch (error) {
      this.logger.warn(`Error parsing HTML content from ${url}:`, error);
      return { isApiDocumentation: false };
    }
  }

  /**
   * Count endpoints in OpenAPI/Swagger paths
   */
  private countEndpoints(paths: any): number {
    if (!paths || typeof paths !== 'object') return 0;
    
    let count = 0;
    for (const path in paths) {
      if (paths[path] && typeof paths[path] === 'object') {
        // Count HTTP methods for each path
        const methods = ['get', 'post', 'put', 'patch', 'delete', 'options', 'head'];
        count += methods.filter(method => paths[path][method]).length;
      }
    }
    return count;
  }

  /**
   * Count endpoints in Postman collection
   */
  private countPostmanEndpoints(items: any[]): number {
    if (!Array.isArray(items)) return 0;
    
    let count = 0;
    for (const item of items) {
      if (item.request) {
        count++;
      } else if (item.item && Array.isArray(item.item)) {
        count += this.countPostmanEndpoints(item.item);
      }
    }
    return count;
  }
}
