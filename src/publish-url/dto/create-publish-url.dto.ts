import { IsNotEmpty, IsS<PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UrlType, UrlContentType } from '../publish-url.entity';

export class CreatePublishUrlDto {
  @ApiProperty({
    description: 'Name of the URL',
    example: 'My Documentation'
  })
  @IsString()
  @IsNotEmpty()
  url_name: string;

  @ApiProperty({
    description: 'Type of URL (private/public)',
    enum: UrlType,
    example: UrlType.PUBLIC
  })
  @IsEnum(UrlType)
  @IsNotEmpty()
  url_type: UrlType;

  @ApiProperty({
    description: 'The actual URL link',
    example: 'https://example.com/docs'
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiPropertyOptional({
    description: 'Content type of the URL (website or api_documentation)',
    enum: UrlContentType,
    example: UrlContentType.WEBSITE
  })
  @IsEnum(UrlContentType)
  @IsOptional()
  content_type?: UrlContentType;

  @ApiPropertyOptional({
    description: 'API documentation metadata (JSON)',
    example: '{"type": "swagger", "version": "3.0", "endpoints": 15}'
  })
  @IsOptional()
  api_metadata?: any;
}