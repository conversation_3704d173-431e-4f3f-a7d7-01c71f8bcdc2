import { IsNotEmpty, <PERSON>Url, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Optional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UrlType } from '../publish-url.entity';

export enum IntegrationType {
  CONFLUENCE = 'confluence',
  ATLASSIAN = 'atlassian',
  JIRA = 'jira',
  SHAREPOINT = 'sharepoint'
}

export class CreatePublishPrivateUrlDto {
  @ApiProperty({
    description: 'Name of the URL',
    example: 'My JIRA Ticket'
  })
  @IsString()
  @IsNotEmpty()
  url_name: string;

  @ApiProperty({
    description: 'Type of URL (private/public)',
    enum: UrlType,
    example: 'private'
  })
  @IsString()
  @IsNotEmpty()
  url_type: string;

  @ApiProperty({
    description: 'The URL to publish',
    example: 'https://your-domain.atlassian.net/browse/PROJECT-123'
  })
  @IsNotEmpty()
  @IsUrl()
  url: string;

  @ApiProperty({
    description: 'The integration type',
    enum: IntegrationType,
    example: 'atlassian'
  })
  @IsNotEmpty()
  @IsEnum(IntegrationType)
  integration: string;

  @ApiProperty({
    description: 'The email for authentication',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsString()
  integration_email: string;

  @ApiProperty({
    description: 'The API token for authentication',
    example: 'your-api-token'
  })
  @IsNotEmpty()
  @IsString()
  integration_api_token: string;
}
